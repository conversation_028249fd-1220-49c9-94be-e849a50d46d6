# reCAPTCHA v3 Enterprise 优化指南

## 🔍 当前检测到的reCAPTCHA版本

你的项目使用的是 **reCAPTCHA v3 Enterprise** 版本，具有以下特征：

- **版本**: reCAPTCHA v3 Enterprise
- **Site Key**: `6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1`
- **特点**: 无感验证，基于行为评分 (0.0-1.0)
- **当前最低分数要求**: 0.3

## 🎯 当前配置

### ⚡ 强制最高分数模式 (已启用)
系统现在**强制使用**最高分数的reCAPTCHA类型：

```javascript
RecaptchaV3TaskProxylessM1S9   // 35 points, 强制0.9分
```

### 1. 动态pageAction检测
系统会自动检测正确的pageAction值：
- 检查`grecaptcha.enterprise.execute`调用
- 检查`data-action`属性
- 检查常见action值模式
- 默认使用`submit`

### 2. 最高分数保证
- **强制0.9分**: 确保获得最高质量的reCAPTCHA token
- **35 points消耗**: 虽然消耗更多points，但成功率最高
- **无降级**: 不会尝试其他较低分数的类型

## 🚀 使用方法

### 直接运行 (已强制启用最高分数模式)

```bash
# 运行邮箱验证 - 自动使用 RecaptchaV3TaskProxylessM1S9
npm run email-verification
```

### 在代码中使用

```javascript
const autoRegister = new AutoRegister();

// 系统已自动配置为最高分数模式，无需额外设置
const result = await autoRegister.handleEmailVerificationWithOneMailAPI(url);
```

### 直接使用CaptchaHandler

```javascript
const captchaHandler = new CaptchaHandler();

// 系统已自动配置为强制0.9分模式
const success = await captchaHandler.handleRecaptchaEnterprise(page);
```

## 📊 YesCaptcha类型对比

| 类型 | Points | 描述 | 适用场景 |
|------|--------|------|----------|
| RecaptchaV3TaskProxyless | 20 | 标准架构 | 一般网站 |
| RecaptchaV3TaskProxylessM1 | 25 | M1架构 | 要求较高的网站 |
| RecaptchaV3TaskProxylessM1S7 | 30 | M1架构+强制0.7分 | 高要求网站 |
| RecaptchaV3TaskProxylessM1S9 | 35 | M1架构+强制0.9分 | 极高要求网站 |

## ⚙️ 配置说明

### 当前配置
```javascript
this.yesCaptchaKey = 'b289ed1345b8af1fd74244a0438aac098f9a3b2376209';
this.recaptchaSiteKey = '6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1';
```

### 延迟接管策略
系统使用延迟接管策略来提高成功率：
1. 让reCAPTCHA先收集行为数据 (3秒)
2. 并行获取YesCaptcha token
3. 在关键时刻替换token
4. 保持原有的提交流程

## 🔧 故障排除

### 如果通过率较低
1. **启用高分数模式**:
   ```javascript
   autoRegister.enableHighScoreCaptchaMode();
   ```

2. **检查pageAction值**:
   系统会自动检测，但你也可以手动检查页面源码中的action值

3. **查看日志**:
   系统会显示使用的YesCaptcha类型和响应信息

### 常见问题
- **部分成功部分失败**: 这是正常的，reCAPTCHA v3的通过率不是100%
- **分数要求变化**: 目标网站可能会动态调整分数要求
- **Enterprise版本要求更高**: 比普通v3版本检测更严格

## 📝 使用建议

1. **先尝试标准模式**: 大多数情况下已经足够
2. **遇到困难时启用高分数模式**: 消耗更多points但成功率更高
3. **监控日志**: 观察哪种类型成功率最高
4. **根据网站调整**: 不同网站可能需要不同的策略

## 🎉 总结

你的reCAPTCHA v3 Enterprise处理系统现在具备：
- ✅ 自动pageAction检测
- ✅ 多种YesCaptcha类型支持
- ✅ 高分数模式选项
- ✅ 智能降级策略
- ✅ 详细的调试日志

这应该能显著提高你的reCAPTCHA通过率！
