const fs = require('fs');
const path = require('path');

/**
 * Real Browser Logger
 * 专门为真实浏览器自动化设计的日志记录器
 * 每一步都会保存HTML和截图
 */
class RealBrowserLogger {
    constructor() {
        this.stepCounter = 0;
        this.startTime = Date.now();
        
        // 创建目录
        this.imageDir = path.join(__dirname, 'screenshots');
        this.htmlDir = path.join(__dirname, 'html');
        this.logDir = path.join(__dirname, 'logs');
        
        this.ensureDirectories();
        
        // 日志文件
        this.logFile = path.join(this.logDir, `real-browser-${this.getDateString()}.log`);
        
        this.log('🚀 Real Browser Logger 初始化完成');
    }
    
    ensureDirectories() {
        [this.imageDir, this.htmlDir, this.logDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }
    
    getDateString() {
        const now = new Date();
        return now.toISOString().split('T')[0];
    }
    
    getTimeString() {
        return new Date().toLocaleTimeString();
    }
    
    log(message, level = 'INFO') {
        const timestamp = this.getTimeString();
        const logMessage = `[${timestamp}] [${level}] ${message}`;
        
        console.log(logMessage);
        
        // 写入日志文件
        fs.appendFileSync(this.logFile, logMessage + '\n');
    }
    
    error(message, error = null) {
        this.log(message, 'ERROR');
        if (error) {
            this.log(`Error details: ${error.message}`, 'ERROR');
            this.log(`Stack trace: ${error.stack}`, 'ERROR');
        }
    }
    
    warn(message) {
        this.log(message, 'WARN');
    }
    
    debug(message) {
        this.log(message, 'DEBUG');
    }
    
    async captureStep(page, stepName, description = '') {
        this.stepCounter++;
        const stepId = `STEP_${this.stepCounter.toString().padStart(2, '0')}_${stepName}`;
        
        this.log(`📸 步骤 ${this.stepCounter}: ${stepName} - ${description}`);
        
        try {
            // 截图
            const screenshotPath = path.join(this.imageDir, `${stepId}.png`);
            await page.screenshot({ 
                path: screenshotPath, 
                fullPage: true,
                type: 'png'
            });
            
            // 保存HTML
            const htmlContent = await page.content();
            const htmlPath = path.join(this.htmlDir, `${stepId}.html`);
            fs.writeFileSync(htmlPath, htmlContent);
            
            this.log(`✅ 步骤 ${this.stepCounter} 记录完成:`);
            this.log(`   📸 截图: ${screenshotPath}`);
            this.log(`   📄 HTML: ${htmlPath}`);
            
            return {
                stepId,
                screenshotPath,
                htmlPath,
                stepNumber: this.stepCounter
            };
            
        } catch (error) {
            this.error(`❌ 步骤 ${this.stepCounter} 记录失败: ${stepName}`, error);
            throw error;
        }
    }
    
    async captureError(page, errorName, errorMessage = '') {
        const errorId = `ERROR_${this.stepCounter}_${errorName}`;
        
        this.error(`💥 错误捕获: ${errorName} - ${errorMessage}`);
        
        try {
            // 错误截图
            const screenshotPath = path.join(this.imageDir, `${errorId}.png`);
            await page.screenshot({ 
                path: screenshotPath, 
                fullPage: true,
                type: 'png'
            });
            
            // 错误HTML
            const htmlContent = await page.content();
            const htmlPath = path.join(this.htmlDir, `${errorId}.html`);
            fs.writeFileSync(htmlPath, htmlContent);
            
            this.error(`💾 错误信息已保存:`);
            this.error(`   📸 截图: ${screenshotPath}`);
            this.error(`   📄 HTML: ${htmlPath}`);
            
            return {
                errorId,
                screenshotPath,
                htmlPath
            };
            
        } catch (captureError) {
            this.error(`❌ 错误捕获失败`, captureError);
        }
    }
    
    logFlowStart() {
        this.log('🎬 Real Browser 自动化流程开始');
        this.log(`⏰ 开始时间: ${new Date().toLocaleString()}`);
    }
    
    logFlowEnd(success = true) {
        const duration = Date.now() - this.startTime;
        const durationStr = `${(duration / 1000).toFixed(2)}秒`;
        
        if (success) {
            this.log(`🎉 Real Browser 自动化流程成功完成`);
            this.log(`⏱️ 总耗时: ${durationStr}`);
            this.log(`📊 总步骤数: ${this.stepCounter}`);
        } else {
            this.error(`💥 Real Browser 自动化流程失败`);
            this.error(`⏱️ 失败前耗时: ${durationStr}`);
            this.error(`📊 执行步骤数: ${this.stepCounter}`);
        }
        
        this.log(`📁 查看详细记录:`);
        this.log(`   📸 截图目录: ${this.imageDir}`);
        this.log(`   📄 HTML目录: ${this.htmlDir}`);
        this.log(`   📋 日志文件: ${this.logFile}`);
    }
    
    getStats() {
        const duration = Date.now() - this.startTime;
        return {
            stepCount: this.stepCounter,
            duration: duration,
            durationStr: `${(duration / 1000).toFixed(2)}秒`,
            imageDir: this.imageDir,
            htmlDir: this.htmlDir,
            logFile: this.logFile
        };
    }
}

module.exports = RealBrowserLogger;
