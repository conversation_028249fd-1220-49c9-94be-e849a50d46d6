# DrissionPage Automation 使用指南

## 快速开始

### 1. 安装依赖

```bash
cd drissionpage-automation
pip install -r requirements.txt
```

### 2. 配置环境

在根目录的 `.env` 文件中添加配置：

```env
# DrissionPage 配置
DRISSIONPAGE_PROXY=false
DRISSIONPAGE_RECAPTCHA_SOLVE=true

# YesCaptcha 配置
YESCAPTCHA_CLIENT_KEY=your_yescaptcha_key_here

# 调试配置
DEBUG_MODE=true
SAVE_SCREENSHOTS=true
SAVE_HTML=true
```

### 3. 测试环境

```bash
python test_setup.py
```

### 4. 运行自动化

```bash
python run_drissionpage_verification.py
```

## 详细配置

### 环境变量说明

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `DRISSIONPAGE_PROXY` | `false` | 是否启用代理 |
| `DRISSIONPAGE_RECAPTCHA_SOLVE` | `false` | 是否自动解决 reCAPTCHA |
| `PROXY_URL` | - | 代理服务器地址 (host:port) |
| `PROXY_USER` | - | 代理用户名 |
| `PROXY_PASS` | - | 代理密码 |
| `YESCAPTCHA_CLIENT_KEY` | - | YesCaptcha API 密钥 |
| `DEBUG_MODE` | `true` | 是否启用调试模式 |
| `SAVE_SCREENSHOTS` | `true` | 是否保存截图 |
| `SAVE_HTML` | `true` | 是否保存 HTML |
| `PAGE_TIMEOUT` | `30000` | 页面超时时间 (毫秒) |
| `EMAIL_CHECK_TIMEOUT` | `120000` | 邮箱检查超时时间 (毫秒) |
| `EMAIL_CHECK_INTERVAL` | `5000` | 邮箱检查间隔 (毫秒) |

### 代理配置示例

```env
DRISSIONPAGE_PROXY=true
PROXY_URL=proxy.example.com:8080
PROXY_USER=username
PROXY_PASS=password
```

## 自动化流程

### 完整流程步骤

1. **生成授权URL** - 创建 Augment OAuth 授权链接
2. **生成临时邮箱** - 使用 OneMail API 生成临时邮箱
3. **启动浏览器** - 初始化 DrissionPage 浏览器
4. **导航到页面** - 访问授权页面
5. **输入邮箱** - 填入临时邮箱地址
6. **处理验证码** - 自动解决 Turnstile 验证码
7. **点击继续** - 提交邮箱表单
8. **等待验证页面** - 等待验证码输入页面
9. **获取验证码** - 从邮箱获取验证码
10. **输入验证码** - 填入验证码
11. **点击继续** - 提交验证码
12. **等待授权页面** - 等待授权码页面
13. **提取授权码** - 获取授权码数据
14. **完成OAuth** - 调用 Augment API 获取令牌
15. **保存令牌** - 将令牌保存到 tokens.json

### 关键技术点

#### 验证码处理

- **自动检测**: 检测页面中的 Turnstile 和 reCAPTCHA
- **YesCaptcha 集成**: 使用 YesCaptcha API 自动解决验证码
- **Token 注入**: 将获得的 token 注入到页面表单中
- **字段同步**: 确保 token 在正确的表单字段中

#### 授权码提取

- **多策略提取**: 使用多种策略确保成功提取授权码
- **剪贴板拦截**: 劫持 clipboard.writeText 获取复制内容
- **JavaScript 提取**: 从页面脚本中提取授权码数据
- **正则匹配**: 从页面内容中提取授权码信息

## 调试和故障排除

### 调试文件

运行后会生成以下调试文件：

```
drissionpage-automation/
├── logs/
│   └── drissionpage-2024-01-01.log    # 详细日志
├── screenshots/
│   ├── STEP_01_browser_started.png    # 各步骤截图
│   ├── STEP_02_page_loaded.png
│   └── ...
└── html/
    ├── STEP_01_browser_started.html    # 各步骤HTML
    ├── STEP_02_page_loaded.html
    └── ...
```

### 常见问题

#### 1. 浏览器启动失败

**问题**: `❌ DrissionPage 浏览器启动失败`

**解决方案**:
- 确保已安装 Chrome/Chromium 浏览器
- 检查系统权限
- 尝试添加更多浏览器参数

```python
options.add_argument('--no-sandbox')
options.add_argument('--disable-setuid-sandbox')
```

#### 2. 验证码解决失败

**问题**: `❌ 验证码处理失败`

**解决方案**:
- 检查 YesCaptcha API 密钥是否正确
- 确保 YesCaptcha 账户有足够余额
- 检查网络连接

#### 3. 邮箱验证码获取失败

**问题**: `未能获取到验证码`

**解决方案**:
- 检查邮箱服务是否正常
- 增加等待时间
- 检查垃圾邮件文件夹

#### 4. 授权码提取失败

**问题**: `未能提取到授权码`

**解决方案**:
- 检查页面是否完全加载
- 查看截图和 HTML 文件
- 检查页面结构是否发生变化

### 性能优化

#### 1. 超时设置

根据网络环境调整超时设置：

```env
PAGE_TIMEOUT=60000          # 慢网络环境
EMAIL_CHECK_TIMEOUT=300000  # 延长邮箱检查时间
```

#### 2. 代理配置

使用高质量代理提高成功率：

```env
DRISSIONPAGE_PROXY=true
PROXY_URL=high-quality-proxy:port
```

#### 3. 重试机制

在代码中添加重试逻辑：

```python
for attempt in range(3):
    try:
        result = automation.some_operation()
        break
    except Exception as e:
        if attempt == 2:
            raise e
        time.sleep(5)
```

## 高级用法

### 自定义处理器

可以扩展现有的处理器：

```python
from handlers import CaptchaHandler

class CustomCaptchaHandler(CaptchaHandler):
    def handle_custom_captcha(self, page):
        # 自定义验证码处理逻辑
        pass
```

### 批量处理

创建批量处理脚本：

```python
def batch_verification(count):
    for i in range(count):
        try:
            run_drissionpage_verification()
            print(f"✅ 第 {i+1} 次验证成功")
        except Exception as e:
            print(f"❌ 第 {i+1} 次验证失败: {e}")
```

### 监控和统计

添加监控功能：

```python
import time
from datetime import datetime

class VerificationMonitor:
    def __init__(self):
        self.start_time = time.time()
        self.success_count = 0
        self.failure_count = 0
    
    def record_success(self):
        self.success_count += 1
    
    def record_failure(self):
        self.failure_count += 1
    
    def get_stats(self):
        duration = time.time() - self.start_time
        total = self.success_count + self.failure_count
        success_rate = self.success_count / total if total > 0 else 0
        
        return {
            'duration': duration,
            'total': total,
            'success': self.success_count,
            'failure': self.failure_count,
            'success_rate': success_rate
        }
```

## 与其他版本对比

| 特性 | real-browser-automation | drissionpage-automation |
|------|------------------------|------------------------|
| 语言 | JavaScript (Node.js) | Python |
| 浏览器库 | puppeteer-real-browser | DrissionPage |
| 学习曲线 | 中等 | 简单 |
| 生态系统 | Node.js 生态 | Python 生态 |
| 性能 | 高 | 高 |
| 跨平台 | ✅ | ✅ |
| 调试友好 | ✅ | ✅ |

选择建议：
- **JavaScript 开发者**: 选择 `real-browser-automation`
- **Python 开发者**: 选择 `drissionpage-automation`
- **团队协作**: 根据团队技术栈选择
- **性能要求**: 两者性能相近，选择熟悉的语言
