/**
 * Verisoul 逆向工程分析工具
 * 用于分析和对抗 Verisoul 反欺诈检测系统
 */

class VerisoulReverseEngineering {
    constructor() {
        this.detectedFunctions = new Map();
        this.networkRequests = [];
        this.behaviorData = [];
        this.isAnalyzing = false;
        this.originalFunctions = new Map();
    }

    /**
     * 启动 Verisoul 逆向分析
     */
    async startAnalysis() {
        console.log('🔍 开始 Verisoul 逆向分析...');
        
        this.isAnalyzing = true;
        
        // 1. 拦截 Verisoul 对象创建
        this.interceptVerisoulObject();
        
        // 2. Hook 关键 API
        this.hookCriticalAPIs();
        
        // 3. 监控网络请求
        this.monitorNetworkRequests();
        
        // 4. 分析检测函数
        this.analyzeDetectionFunctions();
        
        // 5. 启动实时监控
        this.startRealTimeMonitoring();
        
        console.log('✅ Verisoul 逆向分析已启动');
    }

    /**
     * 拦截 Verisoul 对象创建
     */
    interceptVerisoulObject() {
        const self = this;
        
        // 拦截 Verisoul 对象
        Object.defineProperty(window, 'Verisoul', {
            get() {
                return this._verisoul;
            },
            set(value) {
                console.log('🎯 Verisoul 对象被创建:', value);
                
                // 分析 Verisoul 对象的所有方法
                if (value && typeof value === 'object') {
                    self.analyzeVerisoulObject(value);
                }
                
                this._verisoul = value;
            },
            configurable: true
        });
    }

    /**
     * 分析 Verisoul 对象
     */
    analyzeVerisoulObject(verisoulObj) {
        console.log('📊 分析 Verisoul 对象结构...');
        
        const methods = Object.getOwnPropertyNames(verisoulObj);
        console.log('🔧 发现的方法:', methods);
        
        // Hook 所有方法
        methods.forEach(methodName => {
            if (typeof verisoulObj[methodName] === 'function') {
                this.hookVerisoulMethod(verisoulObj, methodName);
            }
        });
        
        // 特别关注 session 方法
        if (verisoulObj.session) {
            this.analyzeSessionMethod(verisoulObj);
        }
    }

    /**
     * Hook Verisoul 方法
     */
    hookVerisoulMethod(obj, methodName) {
        const originalMethod = obj[methodName];
        const self = this;
        
        obj[methodName] = function(...args) {
            console.log(`🎣 ${methodName} 被调用:`, args);
            
            // 记录调用信息
            self.detectedFunctions.set(methodName, {
                args: args,
                timestamp: Date.now(),
                callStack: new Error().stack
            });
            
            // 调用原始方法
            const result = originalMethod.apply(this, args);
            
            console.log(`📤 ${methodName} 返回:`, result);
            
            return result;
        };
    }

    /**
     * 分析 session 方法
     */
    analyzeSessionMethod(verisoulObj) {
        const originalSession = verisoulObj.session;
        const self = this;
        
        verisoulObj.session = async function(...args) {
            console.log('🔐 session() 方法被调用:', args);
            
            try {
                const result = await originalSession.apply(this, args);
                console.log('🔑 session() 返回结果:', result);
                
                // 分析 session 数据
                self.analyzeSessionData(result);
                
                return result;
            } catch (error) {
                console.log('❌ session() 调用失败:', error);
                throw error;
            }
        };
    }

    /**
     * 分析 session 数据
     */
    analyzeSessionData(sessionData) {
        console.log('📋 分析 session 数据:', sessionData);
        
        if (sessionData && sessionData.session_id) {
            console.log('🆔 Session ID:', sessionData.session_id);
            
            // 尝试解析更多信息
            try {
                const decoded = this.decodeSessionData(sessionData);
                console.log('🔓 解码后的数据:', decoded);
            } catch (error) {
                console.log('⚠️ 无法解码 session 数据:', error);
            }
        }
    }

    /**
     * Hook 关键 API
     */
    hookCriticalAPIs() {
        console.log('🎣 Hook 关键 API...');
        
        // Hook navigator 属性访问
        this.hookNavigatorProperties();
        
        // Hook Canvas API
        this.hookCanvasAPI();
        
        // Hook WebGL API
        this.hookWebGLAPI();
        
        // Hook 事件监听器
        this.hookEventListeners();
        
        // Hook 网络请求
        this.hookNetworkAPIs();
    }

    /**
     * Hook Navigator 属性
     */
    hookNavigatorProperties() {
        const sensitiveProps = [
            'userAgent', 'platform', 'language', 'languages',
            'hardwareConcurrency', 'deviceMemory', 'maxTouchPoints',
            'webdriver', 'plugins', 'mimeTypes'
        ];
        
        sensitiveProps.forEach(prop => {
            if (prop in navigator) {
                const originalValue = navigator[prop];
                let accessCount = 0;
                
                Object.defineProperty(navigator, prop, {
                    get() {
                        accessCount++;
                        console.log(`🔍 navigator.${prop} 被访问 (第${accessCount}次):`, originalValue);
                        return originalValue;
                    },
                    configurable: true
                });
            }
        });
    }

    /**
     * Hook Canvas API
     */
    hookCanvasAPI() {
        const originalGetContext = HTMLCanvasElement.prototype.getContext;
        const self = this;
        
        HTMLCanvasElement.prototype.getContext = function(contextType, ...args) {
            console.log('🎨 Canvas getContext 被调用:', contextType);
            
            const context = originalGetContext.call(this, contextType, ...args);
            
            if (contextType === '2d' && context) {
                self.hookCanvas2D(context);
            } else if (contextType.includes('webgl') && context) {
                self.hookWebGLContext(context);
            }
            
            return context;
        };
    }

    /**
     * Hook Canvas 2D 上下文
     */
    hookCanvas2D(context) {
        const methods = ['fillText', 'strokeText', 'getImageData', 'toDataURL'];
        
        methods.forEach(method => {
            if (context[method]) {
                const originalMethod = context[method];
                
                context[method] = function(...args) {
                    console.log(`🖼️ Canvas 2D ${method} 被调用:`, args);
                    return originalMethod.apply(this, args);
                };
            }
        });
    }

    /**
     * Hook WebGL 上下文
     */
    hookWebGLContext(context) {
        const methods = ['getParameter', 'getExtension', 'getSupportedExtensions'];
        
        methods.forEach(method => {
            if (context[method]) {
                const originalMethod = context[method];
                
                context[method] = function(...args) {
                    const result = originalMethod.apply(this, args);
                    console.log(`🎮 WebGL ${method} 被调用:`, args, '返回:', result);
                    return result;
                };
            }
        });
    }

    /**
     * Hook 事件监听器
     */
    hookEventListeners() {
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (['mousemove', 'keydown', 'keyup', 'click', 'touchstart', 'touchmove'].includes(type)) {
                console.log(`👂 事件监听器被添加: ${type}`);
            }
            
            return originalAddEventListener.call(this, type, listener, options);
        };
    }

    /**
     * Hook 网络 API
     */
    hookNetworkAPIs() {
        // Hook fetch
        const originalFetch = window.fetch;
        const self = this;
        
        window.fetch = function(url, options) {
            if (url.includes('verisoul.ai')) {
                console.log('🌐 Verisoul 网络请求:', url, options);
                
                self.networkRequests.push({
                    url: url,
                    options: options,
                    timestamp: Date.now(),
                    type: 'fetch'
                });
            }
            
            return originalFetch.apply(this, arguments);
        };
        
        // Hook WebSocket
        const originalWebSocket = window.WebSocket;
        
        window.WebSocket = function(url, protocols) {
            if (url.includes('verisoul.ai')) {
                console.log('🔌 Verisoul WebSocket 连接:', url);
                
                self.networkRequests.push({
                    url: url,
                    protocols: protocols,
                    timestamp: Date.now(),
                    type: 'websocket'
                });
            }
            
            return new originalWebSocket(url, protocols);
        };
    }

    /**
     * 监控网络请求
     */
    monitorNetworkRequests() {
        console.log('📡 开始监控网络请求...');
        
        // 使用 Performance Observer 监控网络性能
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach(entry => {
                    if (entry.name.includes('verisoul.ai')) {
                        console.log('📊 Verisoul 网络性能:', entry);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['resource'] });
        }
    }

    /**
     * 分析检测函数
     */
    analyzeDetectionFunctions() {
        console.log('🔬 分析检测函数...');
        
        // 搜索可能的检测函数
        this.searchDetectionFunctions();
        
        // 分析函数调用模式
        this.analyzeCallPatterns();
    }

    /**
     * 搜索检测函数
     */
    searchDetectionFunctions() {
        const detectionKeywords = [
            'webdriver', 'selenium', 'automation', 'phantom', 'headless',
            'bot', 'crawler', 'virtual', 'emulator', 'fingerprint'
        ];
        
        // 搜索全局对象中的相关函数
        for (const key in window) {
            if (typeof window[key] === 'function') {
                const funcStr = window[key].toString().toLowerCase();
                
                detectionKeywords.forEach(keyword => {
                    if (funcStr.includes(keyword)) {
                        console.log(`🎯 发现可疑检测函数: ${key} (包含关键词: ${keyword})`);
                        this.detectedFunctions.set(key, {
                            type: 'detection',
                            keyword: keyword,
                            source: funcStr.substring(0, 200) + '...'
                        });
                    }
                });
            }
        }
    }

    /**
     * 分析调用模式
     */
    analyzeCallPatterns() {
        console.log('📈 分析函数调用模式...');
        
        // 这里可以添加更复杂的模式分析逻辑
        // 例如分析函数调用频率、时序等
    }

    /**
     * 启动实时监控
     */
    startRealTimeMonitoring() {
        console.log('⏰ 启动实时监控...');
        
        setInterval(() => {
            if (this.isAnalyzing) {
                this.generateAnalysisReport();
            }
        }, 10000); // 每10秒生成一次报告
    }

    /**
     * 生成分析报告
     */
    generateAnalysisReport() {
        const report = {
            timestamp: new Date().toISOString(),
            detectedFunctions: Array.from(this.detectedFunctions.entries()),
            networkRequests: this.networkRequests.slice(-10), // 最近10个请求
            behaviorData: this.behaviorData.slice(-20) // 最近20个行为数据
        };
        
        console.log('📋 Verisoul 分析报告:', report);
        
        return report;
    }

    /**
     * 尝试解码 session 数据
     */
    decodeSessionData(sessionData) {
        // 这里可以添加更复杂的解码逻辑
        // 例如 Base64 解码、JSON 解析等
        
        if (typeof sessionData === 'string') {
            try {
                return JSON.parse(sessionData);
            } catch (e) {
                try {
                    return atob(sessionData);
                } catch (e2) {
                    return sessionData;
                }
            }
        }
        
        return sessionData;
    }

    /**
     * 停止分析
     */
    stopAnalysis() {
        console.log('🛑 停止 Verisoul 逆向分析');
        this.isAnalyzing = false;
    }

    /**
     * 导出分析结果
     */
    exportResults() {
        const results = {
            detectedFunctions: Object.fromEntries(this.detectedFunctions),
            networkRequests: this.networkRequests,
            behaviorData: this.behaviorData,
            analysisComplete: !this.isAnalyzing
        };
        
        console.log('💾 导出分析结果:', results);
        return results;
    }
}

// 自动启动分析（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    window.VerisoulReverseEngineering = VerisoulReverseEngineering;
    
    // 创建全局实例
    window.verisoulAnalyzer = new VerisoulReverseEngineering();
    
    console.log('🚀 Verisoul 逆向工程工具已加载');
    console.log('使用 window.verisoulAnalyzer.startAnalysis() 开始分析');
}

module.exports = VerisoulReverseEngineering;
