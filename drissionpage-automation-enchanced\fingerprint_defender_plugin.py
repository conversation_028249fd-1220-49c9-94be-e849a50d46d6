#!/usr/bin/env python3
"""
FingerprintDefender 插件集成模块
用于在 DrissionPage 中加载 FingerprintDefender 浏览器扩展
"""

import os
import json
import shutil
import zipfile
import requests
from pathlib import Path
from drissionpage_logger import DrissionPageLogger

class FingerprintDefenderPlugin:
    """Canvas Fingerprint Defender 插件管理器"""

    def __init__(self, logger=None):
        self.logger = logger or DrissionPageLogger()
        self.plugin_dir = Path(__file__).parent / 'canvas_fingerprint_defender'
        self.plugin_id = 'lanfdkkpgfjfdikkncbnojekcppdebfp'  # Canvas Fingerprint Defender 插件 ID
        self.plugin_url = f'https://chromewebstore.google.com/detail/canvas-fingerprint-defend/{self.plugin_id}'
        
    def setup_plugin_directory(self, plugin_source=None):
        """设置插件目录"""
        try:
            self.logger.log('🛡️ 设置 FingerprintDefender 插件目录...')
            
            # 创建插件目录
            self.plugin_dir.mkdir(parents=True, exist_ok=True)
            
            if plugin_source:
                if isinstance(plugin_source, str) and plugin_source.endswith('.crx'):
                    # 从 .crx 文件加载
                    self._extract_crx_file(plugin_source)
                elif isinstance(plugin_source, str) and os.path.isdir(plugin_source):
                    # 从目录复制
                    self._copy_plugin_directory(plugin_source)
                else:
                    self.logger.log('⚠️ 不支持的插件源格式')
                    return False
            else:
                # 创建基本的插件结构（如果没有提供插件源）
                self._create_basic_plugin()
            
            self.logger.log(f'✅ FingerprintDefender 插件目录已设置: {self.plugin_dir}')
            return True
            
        except Exception as e:
            self.logger.log(f'❌ 插件目录设置失败: {e}')
            return False
    
    def _extract_crx_file(self, crx_path):
        """从 .crx 文件提取插件"""
        self.logger.log(f'📦 从 CRX 文件提取插件: {crx_path}')

        try:
            # CRX 文件实际上是一个特殊格式的 ZIP 文件
            # 需要跳过 CRX 头部，然后解压 ZIP 内容
            with open(crx_path, 'rb') as f:
                # 读取 CRX 头部
                magic = f.read(4)
                if magic != b'Cr24':
                    # 如果不是 CRX 格式，尝试作为普通 ZIP 文件处理
                    self.logger.log('⚠️ 不是标准 CRX 格式，尝试作为 ZIP 文件处理')
                    f.seek(0)
                    zip_data = f.read()
                else:
                    # 标准 CRX 格式处理
                    version = int.from_bytes(f.read(4), 'little')
                    pub_key_len = int.from_bytes(f.read(4), 'little')
                    sig_len = int.from_bytes(f.read(4), 'little')

                    # 跳过公钥和签名
                    f.seek(pub_key_len + sig_len, 1)

                    # 剩余的就是 ZIP 数据
                    zip_data = f.read()

            # 解压到插件目录
            import io
            try:
                with zipfile.ZipFile(io.BytesIO(zip_data)) as zip_file:
                    zip_file.extractall(self.plugin_dir)
                self.logger.log(f'✅ CRX 文件解压成功')
            except zipfile.BadZipFile:
                # 如果 ZIP 解压失败，尝试直接重命名文件
                self.logger.log('⚠️ ZIP 解压失败，尝试直接使用 CRX 文件')
                # 创建基本插件结构
                self._create_basic_plugin()

        except Exception as e:
            self.logger.log(f'⚠️ CRX 文件处理失败: {e}，使用基本插件')
            self._create_basic_plugin()
    
    def _copy_plugin_directory(self, source_dir):
        """从目录复制插件"""
        self.logger.log(f'📁 从目录复制插件: {source_dir}')
        
        # 清空目标目录
        if self.plugin_dir.exists():
            shutil.rmtree(self.plugin_dir)
        
        # 复制插件文件
        shutil.copytree(source_dir, self.plugin_dir)
    
    def _create_basic_plugin(self):
        """创建基本的指纹防护插件（如果没有提供真实插件）"""
        self.logger.log('🔧 创建基本指纹防护插件...')
        
        # 创建 manifest.json
        manifest = {
            "manifest_version": 3,
            "name": "Basic Fingerprint Defender",
            "version": "1.0.0",
            "description": "Basic fingerprint protection for automation",
            "permissions": [
                "scripting",
                "activeTab"
            ],
            "host_permissions": ["<all_urls>"],
            "background": {
                "service_worker": "background.js"
            },
            "content_scripts": [
                {
                    "matches": ["<all_urls>"],
                    "js": ["content.js"],
                    "run_at": "document_start"
                }
            ]
        }
        
        with open(self.plugin_dir / 'manifest.json', 'w', encoding='utf-8') as f:
            json.dump(manifest, f, indent=2)
        
        # 创建 background.js
        background_js = """
// Basic Fingerprint Defender Background Script
console.log('🛡️ Basic Fingerprint Defender loaded');

// 监听页面加载
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'loading' && tab.url) {
        // 注入防护脚本
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: injectFingerprintProtection
        }).catch(err => {
            console.log('Script injection failed:', err);
        });
    }
});

function injectFingerprintProtection() {
    // 基本的指纹防护
    try {
        // 移除 webdriver 属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });
        
        // 伪装 Chrome 对象
        if (!window.chrome) {
            window.chrome = {
                runtime: {
                    onConnect: undefined,
                    onMessage: undefined
                }
            };
        }
        
        console.log('🛡️ Basic fingerprint protection injected');
    } catch (e) {
        console.log('Fingerprint protection error:', e);
    }
}
"""
        
        with open(self.plugin_dir / 'background.js', 'w', encoding='utf-8') as f:
            f.write(background_js)
        
        # 创建 content.js
        content_js = """
// Enhanced Fingerprint Defender Content Script
(function() {
    'use strict';

    console.log('🛡️ Enhanced Fingerprint Defender content script loaded');

    // 移除 WebDriver 相关属性
    try {
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });

        // 清理 Selenium 变量
        const seleniumVars = [
            '$cdc_asdjflasutopfhvcZLmcfl_', '$chrome_asyncScriptInfo', '__webdriver_script_fn',
            '__driver_evaluate', '__webdriver_evaluate', '__selenium_evaluate', '__fxdriver_evaluate',
            '__driver_unwrapped', '__webdriver_unwrapped', '__selenium_unwrapped', '__fxdriver_unwrapped',
            '_Selenium_IDE_Recorder', 'calledSelenium', 'calledPhantom', '__nightmare', '_phantom'
        ];

        seleniumVars.forEach(varName => {
            try {
                delete window[varName];
                delete window.document[varName];
            } catch(e) {}
        });

        // 伪装 Chrome 对象
        if (!window.chrome) {
            window.chrome = {
                runtime: {
                    onConnect: undefined,
                    onMessage: undefined
                },
                app: {
                    isInstalled: false,
                    InstallState: {
                        DISABLED: 'disabled',
                        INSTALLED: 'installed',
                        NOT_INSTALLED: 'not_installed'
                    }
                }
            };
        }

        // Canvas 指纹随机化
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function(...args) {
            const context = this.getContext('2d');
            if (context) {
                // 添加随机噪声
                const imageData = context.getImageData(0, 0, this.width, this.height);
                for (let i = 0; i < imageData.data.length; i += 4) {
                    if (Math.random() < 0.01) { // 1% 的像素添加噪声
                        imageData.data[i] = Math.min(255, Math.max(0, imageData.data[i] + (Math.random() * 4 - 2)));
                        imageData.data[i + 1] = Math.min(255, Math.max(0, imageData.data[i + 1] + (Math.random() * 4 - 2)));
                        imageData.data[i + 2] = Math.min(255, Math.max(0, imageData.data[i + 2] + (Math.random() * 4 - 2)));
                    }
                }
                context.putImageData(imageData, 0, 0);
            }
            return originalToDataURL.apply(this, args);
        };

        // Canvas 绘制随机化
        const originalFillText = CanvasRenderingContext2D.prototype.fillText;
        CanvasRenderingContext2D.prototype.fillText = function(text, x, y, maxWidth) {
            const noise = () => Math.random() * 0.1 - 0.05;
            return originalFillText.call(this, text, x + noise(), y + noise(), maxWidth);
        };

        const originalStrokeText = CanvasRenderingContext2D.prototype.strokeText;
        CanvasRenderingContext2D.prototype.strokeText = function(text, x, y, maxWidth) {
            const noise = () => Math.random() * 0.1 - 0.05;
            return originalStrokeText.call(this, text, x + noise(), y + noise(), maxWidth);
        };

        // WebGL 指纹伪装
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            switch (parameter) {
                case this.VENDOR:
                    return 'Google Inc. (Intel)';
                case this.RENDERER:
                    return 'ANGLE (Intel, Intel(R) UHD Graphics Direct3D11 vs_5_0 ps_5_0, D3D11)';
                case this.VERSION:
                    return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
                case this.SHADING_LANGUAGE_VERSION:
                    return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
                default:
                    return originalGetParameter.call(this, parameter);
            }
        };

        console.log('🛡️ Enhanced fingerprint protection activated');
    } catch (e) {
        console.log('Fingerprint protection error:', e);
    }
})();
"""
        
        with open(self.plugin_dir / 'content.js', 'w', encoding='utf-8') as f:
            f.write(content_js)
    
    def get_plugin_path(self):
        """获取插件路径"""
        if self.plugin_dir.exists() and (self.plugin_dir / 'manifest.json').exists():
            return str(self.plugin_dir)
        return None
    
    def validate_plugin(self):
        """验证插件是否有效"""
        try:
            manifest_path = self.plugin_dir / 'manifest.json'
            if not manifest_path.exists():
                return False, "manifest.json 不存在"
            
            with open(manifest_path, 'r', encoding='utf-8') as f:
                manifest = json.load(f)
            
            required_fields = ['manifest_version', 'name', 'version']
            for field in required_fields:
                if field not in manifest:
                    return False, f"manifest.json 缺少必需字段: {field}"
            
            return True, "插件验证通过"
            
        except Exception as e:
            return False, f"插件验证失败: {e}"
    
    def cleanup(self):
        """清理插件文件"""
        try:
            if self.plugin_dir.exists():
                shutil.rmtree(self.plugin_dir)
                self.logger.log('🧹 FingerprintDefender 插件文件已清理')
        except Exception as e:
            self.logger.log(f'⚠️ 插件清理失败: {e}')

def download_canvas_fingerprint_defender():
    """
    Canvas Fingerprint Defender 插件获取指南
    """
    print("📋 Canvas Fingerprint Defender 插件获取指南:")
    print("=" * 60)
    print("🔗 插件链接:")
    print("   https://chromewebstore.google.com/detail/canvas-fingerprint-defend/lanfdkkpgfjfdikkncbnojekcppdebfp")
    print("")
    print("📥 获取方法:")
    print("1. 方法一：使用 Chrome 扩展下载工具")
    print("   - 访问: https://chrome-extension-downloader.com/")
    print("   - 输入插件 ID: lanfdkkpgfjfdikkncbnojekcppdebfp")
    print("   - 下载 .crx 文件")
    print("")
    print("2. 方法二：从已安装的 Chrome 中提取")
    print("   - 在 Chrome 中安装插件")
    print("   - 访问 chrome://extensions/")
    print("   - 开启开发者模式")
    print("   - 找到插件安装目录并复制")
    print("")
    print("3. 方法三：使用开发者模式")
    print("   - 下载插件源码（如果可用）")
    print("   - 解压到文件夹")
    print("   - 在 Chrome 中加载解压的扩展")
    print("")
    print("� 文件放置:")
    print("   将下载的插件文件放在以下位置之一:")
    print("   - canvas_fingerprint_defender.crx (CRX 文件)")
    print("   - canvas_fingerprint_defender/ (解压后的文件夹)")
    print("")
    print("⚠️ 重要提示:")
    print("   - 确保从官方 Chrome Web Store 下载")
    print("   - 插件 ID: lanfdkkpgfjfdikkncbnojekcppdebfp")
    print("   - 验证插件的完整性和安全性")

    return None

def auto_download_canvas_fingerprint_defender(target_dir=None):
    """
    自动下载 Canvas Fingerprint Defender 插件
    """
    if target_dir is None:
        target_dir = Path(__file__).parent
    else:
        target_dir = Path(target_dir)

    plugin_id = 'lanfdkkpgfjfdikkncbnojekcppdebfp'
    crx_filename = 'canvas_fingerprint_defender.crx'
    crx_path = target_dir / crx_filename

    print("🚀 自动下载 Canvas Fingerprint Defender 插件")
    print("=" * 60)
    print(f"插件 ID: {plugin_id}")
    print(f"目标路径: {crx_path}")

    try:
        # 使用 Google Chrome 的官方 CRX 下载 API
        print("\n📥 从 Google Chrome 官方 API 下载...")
        download_url = f"https://clients2.google.com/service/update2/crx?response=redirect&prodversion=91.0.4472.124&acceptformat=crx2,crx3&x=id%3D{plugin_id}%26uc"

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(download_url, headers=headers, timeout=30)

        if response.status_code == 200 and len(response.content) > 1000:  # 确保下载了实际内容
            with open(crx_path, 'wb') as f:
                f.write(response.content)

            print(f"✅ 下载成功！文件大小: {len(response.content)} 字节")
            print(f"📁 保存位置: {crx_path}")

            # 验证文件
            if _verify_crx_file(crx_path):
                print("✅ CRX 文件验证通过")
                return str(crx_path)
            else:
                print("⚠️ CRX 文件验证失败")
                crx_path.unlink()  # 删除无效文件
        else:
            print(f"❌ 下载失败，状态码: {response.status_code}")

        return None

    except Exception as e:
        print(f"❌ 下载过程中出错: {e}")
        return None

def _verify_crx_file(crx_path):
    """验证 CRX 文件的有效性"""
    try:
        with open(crx_path, 'rb') as f:
            # 检查 CRX 文件头
            magic = f.read(4)
            if magic == b'Cr24':
                return True
            elif magic == b'PK\x03\x04':  # ZIP 文件头（某些扩展可能是 ZIP 格式）
                return True
        return False
    except Exception:
        return False

if __name__ == "__main__":
    # 测试插件管理器
    plugin_manager = FingerprintDefenderPlugin()

    # 首先尝试自动下载
    print("🚀 尝试自动下载 Canvas Fingerprint Defender...")
    download_result = auto_download_canvas_fingerprint_defender()

    if download_result:
        print(f"🎉 自动下载成功: {download_result}")
    else:
        print("⚠️ 自动下载失败，使用基本插件")

    # 设置插件
    if plugin_manager.setup_plugin_directory():
        # 验证插件
        is_valid, message = plugin_manager.validate_plugin()
        print(f"插件验证: {message}")

        if is_valid:
            print(f"插件路径: {plugin_manager.get_plugin_path()}")

    # 如果自动下载失败，显示手动指南
    if not download_result:
        download_canvas_fingerprint_defender()
