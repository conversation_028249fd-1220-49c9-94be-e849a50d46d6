#!/usr/bin/env python3
"""
测试 DrissionPage API 的正确用法
"""

def test_drissionpage_api():
    """测试 DrissionPage API"""
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        
        print("🔍 测试 ChromiumOptions API...")
        
        # 创建选项对象
        options = ChromiumOptions()
        
        # 测试可用的方法
        print("可用的方法:")
        methods = [method for method in dir(options) if not method.startswith('_')]
        for method in methods:
            print(f"  - {method}")
        
        # 尝试不同的设置方法
        print("\n🔧 尝试设置选项...")
        
        # 方法1: add_argument
        try:
            options.add_argument('--no-sandbox')
            print("✅ add_argument 方法可用")
        except Exception as e:
            print(f"❌ add_argument 方法失败: {e}")
        
        # 方法2: headless
        try:
            options.headless()
            print("✅ headless 方法可用")
        except Exception as e:
            print(f"❌ headless 方法失败: {e}")
        
        # 方法3: set_user_agent
        try:
            options.set_user_agent('test-agent')
            print("✅ set_user_agent 方法可用")
        except Exception as e:
            print(f"❌ set_user_agent 方法失败: {e}")
        
        # 方法4: user_agent
        try:
            options.user_agent('test-agent')
            print("✅ user_agent 方法可用")
        except Exception as e:
            print(f"❌ user_agent 方法失败: {e}")
        
        print("\n🌐 尝试创建页面...")
        try:
            page = ChromiumPage(addr_or_opts=options)
            print("✅ 页面创建成功")
            
            # 测试页面方法
            print("\n📄 测试页面方法...")
            page_methods = [method for method in dir(page) if not method.startswith('_')]
            print(f"页面可用方法数量: {len(page_methods)}")
            
            # 测试一些常用方法
            if hasattr(page, 'get'):
                print("✅ get 方法可用")
            if hasattr(page, 'quit'):
                print("✅ quit 方法可用")
            if hasattr(page, 'title'):
                print("✅ title 属性可用")
            if hasattr(page, 'url'):
                print("✅ url 属性可用")
            if hasattr(page, 'html'):
                print("✅ html 属性可用")
            if hasattr(page, 'ele'):
                print("✅ ele 方法可用")
            if hasattr(page, 'eles'):
                print("✅ eles 方法可用")
            if hasattr(page, 'run_js'):
                print("✅ run_js 方法可用")
            if hasattr(page, 'set'):
                print("✅ set 属性可用")
                if hasattr(page.set, 'timeouts'):
                    print("✅ set.timeouts 方法可用")
            
            # 关闭页面
            page.quit()
            print("✅ 页面关闭成功")
            
        except Exception as e:
            print(f"❌ 页面创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ DrissionPage API 测试失败: {e}")
        return False

if __name__ == '__main__':
    test_drissionpage_api()
