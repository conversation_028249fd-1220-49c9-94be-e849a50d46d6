const crypto = require('crypto');
const https = require('https');

/**
 * Augment OAuth 授权处理类
 * 基于 augment-token-mng 项目的 Rust 实现转换为 JavaScript
 */
class AugmentAuth {
    constructor() {
        this.CLIENT_ID = 'v';
        this.AUTH_BASE_URL = 'https://auth.augmentcode.com';
        this.oauthState = null;
    }

    /**
     * Base64 URL 编码（无填充）
     * @param {Buffer} data - 要编码的数据
     * @returns {string} Base64 URL 编码的字符串
     */
    base64UrlEncode(data) {
        return data.toString('base64')
            .replace(/\+/g, '-')
            .replace(/\//g, '_')
            .replace(/=/g, '');
    }

    /**
     * 生成随机字节
     * @param {number} length - 字节长度
     * @returns {Buffer} 随机字节
     */
    generateRandomBytes(length) {
        return crypto.randomBytes(length);
    }

    /**
     * 创建 SHA256 哈希
     * @param {string} data - 要哈希的数据
     * @returns {Buffer} SHA256 哈希结果
     */
    sha256Hash(data) {
        return crypto.createHash('sha256').update(data).digest();
    }

    /**
     * 创建 OAuth 状态
     * @returns {Object} OAuth 状态对象
     */
    createOAuthState() {
        console.log('🔐 生成 OAuth 状态参数...');

        // 生成 code_verifier (32字节随机数)
        const codeVerifierBytes = this.generateRandomBytes(32);
        const codeVerifier = this.base64UrlEncode(codeVerifierBytes);

        // 生成 code_challenge (code_verifier 的 SHA256 哈希)
        const codeChallengeBytes = this.sha256Hash(codeVerifier);
        const codeChallenge = this.base64UrlEncode(codeChallengeBytes);

        // 生成 state (8字节随机数)
        const stateBytes = this.generateRandomBytes(8);
        const state = this.base64UrlEncode(stateBytes);

        const creationTime = Date.now();

        const oauthState = {
            codeVerifier,
            codeChallenge,
            state,
            creationTime
        };

        console.log(`📝 Code Verifier: ${codeVerifier.substring(0, 20)}...`);
        console.log(`📝 Code Challenge: ${codeChallenge.substring(0, 20)}...`);
        console.log(`📝 State: ${state}`);

        this.oauthState = oauthState;
        return oauthState;
    }

    /**
     * 生成授权 URL
     * @returns {string} 授权 URL
     */
    generateAuthUrl() {
        if (!this.oauthState) {
            this.createOAuthState();
        }

        const url = new URL('/authorize', this.AUTH_BASE_URL);
        url.searchParams.append('response_type', 'code');
        url.searchParams.append('code_challenge', this.oauthState.codeChallenge);
        url.searchParams.append('client_id', this.CLIENT_ID);
        url.searchParams.append('state', this.oauthState.state);
        url.searchParams.append('prompt', 'login');

        const authUrl = url.toString();
        console.log('🔗 生成的授权 URL:', authUrl);

        return authUrl;
    }

    /**
     * 解析授权码
     * @param {string} codeInput - 授权码 JSON 字符串
     * @returns {Object} 解析后的授权码对象
     */
    parseAuthCode(codeInput) {
        console.log('📋 解析授权码...');
        console.log('原始输入:', codeInput);

        try {
            const parsed = JSON.parse(codeInput);
            console.log('✅ 授权码解析成功:');
            console.log(`  - Code: ${parsed.code?.substring(0, 20)}...`);
            console.log(`  - State: ${parsed.state}`);
            console.log(`  - Tenant URL: ${parsed.tenant_url}`);

            return parsed;
        } catch (error) {
            console.error('❌ 授权码解析失败:', error.message);
            throw new Error(`Failed to parse authorization code: ${error.message}`);
        }
    }

    /**
     * 获取访问令牌
     * @param {string} tenantUrl - 租户 URL
     * @param {string} codeVerifier - 代码验证器
     * @param {string} code - 授权码
     * @returns {Promise<string>} 访问令牌
     */
    async getAccessToken(tenantUrl, codeVerifier, code) {
        console.log('🔑 获取访问令牌...');
        console.log(`  - Tenant URL: ${tenantUrl}`);
        console.log(`  - Code: ${code.substring(0, 20)}...`);
        console.log(`  - Code Verifier: ${codeVerifier.substring(0, 20)}...`);

        const tokenUrl = `${tenantUrl}token`;
        const postData = JSON.stringify({
            grant_type: 'authorization_code',
            client_id: this.CLIENT_ID,
            code_verifier: codeVerifier,
            redirect_uri: '',
            code: code
        });

        console.log(`📡 发送真实 API 请求到: ${tokenUrl}`);
        console.log(`📡 请求数据: ${JSON.stringify(JSON.parse(postData), null, 2)}`);

        return new Promise((resolve, reject) => {
            const url = new URL(tokenUrl);
            const options = {
                hostname: url.hostname,
                port: url.port || 443,
                path: url.pathname,
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(postData)
                }
            };

            const req = https.request(options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    console.log(`📡 API 响应状态: ${res.statusCode}`);
                    console.log(`📡 API 响应头: ${JSON.stringify(res.headers, null, 2)}`);
                    console.log(`📡 API 响应数据: ${data}`);

                    if (res.statusCode === 200) {
                        try {
                            const response = JSON.parse(data);
                            console.log('✅ 真实访问令牌获取成功！');
                            console.log(`🔑 访问令牌: ${response.access_token?.substring(0, 30)}...`);
                            resolve(response.access_token);
                        } catch (error) {
                            console.error('❌ 解析 API 响应失败:', error.message);
                            reject(new Error(`Failed to parse token response: ${error.message}`));
                        }
                    } else {
                        console.error(`❌ API 请求失败: ${res.statusCode} - ${data}`);
                        reject(new Error(`Token request failed: ${res.statusCode} - ${data}`));
                    }
                });
            });

            req.on('error', (error) => {
                console.error('❌ 请求错误:', error.message);
                reject(error);
            });

            req.write(postData);
            req.end();
        });
    }

    /**
     * 完成完整的 OAuth 流程
     * @param {string} authCodeInput - 授权码 JSON 字符串
     * @returns {Promise<Object>} 包含访问令牌和租户 URL 的对象
     */
    async completeOAuthFlow(authCodeInput) {
        console.log('🚀 开始完整的真实 OAuth 流程...');
        console.log('📋 处理从剪贴板获取的真实授权码...');

        if (!this.oauthState) {
            throw new Error('OAuth state not found. Please generate auth URL first.');
        }

        // 解析授权码
        const parsedCode = this.parseAuthCode(authCodeInput);

        // 验证 state
        if (parsedCode.state !== this.oauthState.state) {
            throw new Error('State mismatch. Possible CSRF attack.');
        }
        console.log('✅ State 验证通过 - 这是真实的授权码！');

        // 获取访问令牌 - 调用真实的 Augment API
        console.log('🔄 即将调用真实的 Augment API 获取访问令牌...');
        const accessToken = await this.getAccessToken(
            parsedCode.tenant_url,
            this.oauthState.codeVerifier,
            parsedCode.code
        );

        const result = {
            access_token: accessToken,
            tenant_url: parsedCode.tenant_url,
            oauth_state: this.oauthState,
            parsed_code: parsedCode
        };

        console.log('🎉 真实 OAuth 流程完成！获取到真实访问令牌！');
        console.log(`🔑 真实访问令牌: ${accessToken?.substring(0, 30)}...`);
        return result;
    }
}

module.exports = AugmentAuth;
