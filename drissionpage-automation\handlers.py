import sys
import os
import requests
import time
import json
import secrets
import hashlib
import base64
import urllib.parse
from pathlib import Path

from datetime import datetime, timedelta

# Add parent directory to path to import shared modules
sys.path.append(str(Path(__file__).parent.parent))

class CaptchaHandler:
    """验证码处理器 - Python 版本"""

    def __init__(self):
        self.yes_captcha_key = os.getenv('YESCAPTCHA_CLIENT_KEY', 'b289ed1345b8af1fd74244a0438aac098f9a3b2376209')
        self.default_site_key = '0x4AAAAAAABkMYinukE_rfkN'
        self.recaptcha_site_key = '6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1'
        self.high_score_mode = False
        print('[CAPTCHA] 🎯 已配置为强制使用 RecaptchaV3TaskProxylessM1S9 (35 points, 强制0.9分)')

    def enable_high_score_mode(self):
        """启用高分数模式"""
        self.high_score_mode = True
        print('[CAPTCHA] 🎯 启用高分数模式')

    def handle_turnstile(self, page, website_url, site_key):
        """处理 Turnstile 验证码"""
        try:
            print('[CAPTCHA] 处理Turnstile验证码...')
            print(f'[CAPTCHA] URL: {website_url}')
            print(f'[CAPTCHA] SiteKey: {site_key}')

            task_id = self.create_turnstile_task(website_url, site_key)
            if not task_id:
                print('[CAPTCHA] Turnstile任务创建失败')
                return False

            token = self.get_captcha_result(task_id)
            if not token:
                print('[CAPTCHA] Turnstile token获取失败')
                return False

            print(f'[CAPTCHA] 获取到token: {token[:50]}...')

            injection_result = self.inject_turnstile_token(page, token)
            print(f'[CAPTCHA] Token注入结果: {injection_result}')

            print('[CAPTCHA] ✅ Turnstile处理完成')
            return True

        except Exception as error:
            print(f'[CAPTCHA] Turnstile处理失败: {str(error)}')
            return False

    def handle_recaptcha_enterprise(self, page):
        """处理 reCAPTCHA Enterprise"""
        try:
            print('[CAPTCHA] 处理reCAPTCHA Enterprise验证码...')

            # 获取当前页面URL & 动态解析 pageAction（按YesCaptcha文档）
            current_url = page.url
            try:
                page_action = page.run_js("""
                    try {
                        if (typeof getAction === 'function') return getAction();
                        // fallback: 常见字段
                        if (window.__captchaAction) return window.__captchaAction;
                        return 'submit';
                    } catch (e) { return 'submit'; }
                """) or 'submit'
            except Exception:
                page_action = 'submit'

            task_id = self.create_recaptcha_task(current_url, self.recaptcha_site_key, page_action)
            if not task_id:
                print('[CAPTCHA] reCAPTCHA任务创建失败')
                return False

            try:
                page.run_js(f"console.log('[CAPTCHA] YesCaptcha task created: {task_id}')")
            except Exception:
                pass

            token = self.get_captcha_result(task_id)
            if not token:
                print('[CAPTCHA] reCAPTCHA token获取失败')
                try:
                    page.run_js("console.log('[CAPTCHA] YesCaptcha failed to get token')")
                except Exception:
                    pass
                return False

            print(f'[CAPTCHA] 获取到reCAPTCHA token: {token[:50]}...')
            try:
                page.run_js(f"console.log('[CAPTCHA] YesCaptcha token acquired, length=' + '{len_token}')".replace('{len_token}', str(len(token))))
            except Exception:
                pass

            injection_result = self.inject_recaptcha_token(page, token)
            print(f'[CAPTCHA] reCAPTCHA Token注入结果: {injection_result}')
            try:
                page.run_js("console.log('[CAPTCHA] Token injected and submit patch installed')")
            except Exception:
                pass

            print('[CAPTCHA] ✅ reCAPTCHA Enterprise处理完成')
            return True

        except Exception as error:
            print(f'[CAPTCHA] reCAPTCHA Enterprise处理失败: {str(error)}')
            return False

    def create_turnstile_task(self, website_url, site_key):
        """创建 Turnstile 任务"""
        try:
            url = 'https://api.yescaptcha.com/createTask'
            data = {
                'clientKey': self.yes_captcha_key,
                'task': {
                    'type': 'TurnstileTaskProxyless',
                    'websiteURL': website_url,
                    'websiteKey': site_key
                }
            }

            response = requests.post(url, json=data, timeout=30)
            result = response.json()

            if result.get('errorId') == 0:
                task_id = result.get('taskId')
                print(f'[CAPTCHA] Turnstile任务创建成功，ID: {task_id}')
                return task_id
            else:
                print(f'[CAPTCHA] Turnstile任务创建失败: {result.get("errorDescription")}')
                return None

        except Exception as error:
            print(f'[CAPTCHA] 创建Turnstile任务异常: {str(error)}')
            return None

    def create_recaptcha_task(self, website_url, site_key, page_action):
        """创建 reCAPTCHA v3 Enterprise 任务（按文档要求提供 pageAction）"""
        try:
            url = 'https://api.yescaptcha.com/createTask'
            task_type = 'RecaptchaV3TaskProxylessM1S9' if self.high_score_mode else 'RecaptchaV3TaskProxyless'

            data = {
                'clientKey': self.yes_captcha_key,
                'task': {
                    'type': task_type,
                    'websiteURL': website_url,
                    'websiteKey': site_key,
                    'pageAction': page_action or 'submit'
                }
            }

            if self.high_score_mode:
                data['task']['minScore'] = 0.9

            response = requests.post(url, json=data, timeout=30)
            result = response.json()

            if result.get('errorId') == 0:
                task_id = result.get('taskId')
                print(f'[CAPTCHA] reCAPTCHA任务创建成功，ID: {task_id}')
                return task_id
            else:
                print(f'[CAPTCHA] reCAPTCHA任务创建失败: {result.get("errorDescription")}')
                return None

        except Exception as error:
            print(f'[CAPTCHA] 创建reCAPTCHA任务异常: {str(error)}')
            return None

    def get_captcha_result(self, task_id):
        """获取验证码结果"""
        try:
            url = 'https://api.yescaptcha.com/getTaskResult'

            for attempt in range(60):  # 最多等待60次，每次2秒
                data = {
                    'clientKey': self.yes_captcha_key,
                    'taskId': task_id
                }

                response = requests.post(url, json=data, timeout=30)
                result = response.json()

                if result.get('errorId') == 0:
                    if result.get('status') == 'ready':
                        # YesCaptcha 文档：gRecaptchaResponse 才是返回字段
                        token = result.get('solution', {}).get('gRecaptchaResponse') or result.get('solution', {}).get('token')
                        if token:
                            print(f'[CAPTCHA] 验证码解决成功，耗时: {(attempt + 1) * 2}秒')
                            return token
                    elif result.get('status') == 'processing':
                        print(f'[CAPTCHA] 验证码处理中... ({attempt + 1}/60)')
                        time.sleep(2)
                        continue
                    else:
                        print(f'[CAPTCHA] 验证码任务状态异常: {result.get("status")}')
                        return None
                else:
                    print(f'[CAPTCHA] 获取验证码结果失败: {result.get("errorDescription")}')
                    return None

            print('[CAPTCHA] 验证码解决超时')
            return None

        except Exception as error:
            print(f'[CAPTCHA] 获取验证码结果异常: {str(error)}')
            return None

    def inject_turnstile_token(self, page, token):
        """注入 Turnstile token"""
        try:
            result = page.run_js(f"""
                // 查找或创建 Turnstile 响应字段
                let responseInput = document.querySelector('input[name="cf-turnstile-response"]');
                if (!responseInput) {{
                    responseInput = document.createElement('input');
                    responseInput.type = 'hidden';
                    responseInput.name = 'cf-turnstile-response';
                    document.body.appendChild(responseInput);
                }}

                // 设置 token 值
                responseInput.value = '{token}';

                // 触发事件
                responseInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                responseInput.dispatchEvent(new Event('change', {{ bubbles: true }}));

                return 'Token注入成功';
            """)

            return result

        except Exception as error:
            print(f'[CAPTCHA] 注入Turnstile token失败: {str(error)}')
            return 'Token注入失败'

    def inject_recaptcha_token(self, page, token):
        """注入 reCAPTCHA token，并在提交前最后一刻无感替换，避免干扰评分"""
        try:
            result = page.run_js(f"""
                (function() {{
                    // 1) 查找或创建 reCAPTCHA 响应字段
                    let responseInput = document.querySelector('input[name="g-recaptcha-response"]');
                    if (!responseInput) {{
                        responseInput = document.createElement('input');
                        responseInput.type = 'hidden';
                        responseInput.name = 'g-recaptcha-response';
                        document.body.appendChild(responseInput);
                    }}

                    // 2) 立即设置 token（兼容非自动提交路径）
                    responseInput.value = '{token}';
                    try {{
                        responseInput.dispatchEvent(new Event('input', {{ bubbles: true }}));
                        responseInput.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    }} catch (e) {{}}

                    // 3) 记录到 window，供提交前兜底覆盖
                    try {{ window.__AUG_TOKEN = '{token}'; }} catch (e) {{}}

                    // 4) 仅对当前表单轻量打补丁：不拦截、不阻断，只在 submit 前覆盖隐藏域
                    try {{
                        const form = document.querySelector('#action-form')
                                  || document.querySelector('form[action*="/terms-accept"]')
                                  || document.querySelector('form[action*="/login"]')
                                  || document.querySelector('form');
                        if (form && !form.__augPatched) {{
                            const nativeSubmit = form.submit.bind(form);
                            form.submit = function() {{
                                try {{
                                    const el = document.getElementById('g-recaptcha-response')
                                              || form.querySelector('input[name="g-recaptcha-response"]');
                                    if (el && window.__AUG_TOKEN) el.value = window.__AUG_TOKEN;
                                    const errs = document.getElementById('client-errors');
                                    if (errs) errs.value = '[]';
                                }} catch (e) {{}}
                                return nativeSubmit();
                            }};
                            form.__augPatched = true;
                        }}
                    }} catch (e) {{}}

                    return 'reCAPTCHA Token注入成功';
                }})()
            """)

            return result

        except Exception as error:
            print(f'[CAPTCHA] 注入reCAPTCHA token失败: {str(error)}')
            return 'reCAPTCHA Token注入失败'


class AugmentAuth:
    """Augment 认证处理器 - 纯 Python 实现"""

    def __init__(self):
        import hashlib
        import base64
        import secrets
        import urllib.parse

        # OAuth 配置
        self.client_id = 'v'
        self.redirect_uri = 'https://auth.augmentcode.com/callback'
        self.auth_base_url = 'https://auth.augmentcode.com/authorize'
        self.token_url = 'https://auth.augmentcode.com/oauth/token'

        # 生成 PKCE 参数
        self.code_verifier = self._generate_code_verifier()
        self.code_challenge = self._generate_code_challenge(self.code_verifier)
        self.state = self._generate_state()

    def _generate_code_verifier(self):
        """生成 PKCE code_verifier"""
        return base64.urlsafe_b64encode(secrets.token_bytes(32)).decode('utf-8').rstrip('=')

    def _generate_code_challenge(self, code_verifier):
        """生成 PKCE code_challenge"""
        digest = hashlib.sha256(code_verifier.encode('utf-8')).digest()
        return base64.urlsafe_b64encode(digest).decode('utf-8').rstrip('=')

    def _generate_state(self):
        """生成随机 state"""
        return base64.urlsafe_b64encode(secrets.token_bytes(16)).decode('utf-8').rstrip('=')

    def generate_auth_url(self):
        """生成授权URL（完全对齐 headless/real-browser：自生成，保证 PKCE 一致性）"""
        try:
            # 完全对齐 headless-automation 和 real-browser 的行为：自己生成授权URL
            params = {
                'response_type': 'code',
                'client_id': self.client_id,
                'code_challenge': self.code_challenge,
                'code_challenge_method': 'S256',
                'state': self.state,
                'prompt': 'login'
            }

            query_string = urllib.parse.urlencode(params)
            auth_url = f'{self.auth_base_url}?{query_string}'

            print('[AUTH] 生成授权URL（与 headless/real-browser 一致）')
            print(f'[AUTH] State: {self.state}')
            print(f'[AUTH] Code Challenge: {self.code_challenge[:20]}...')
            print(f'[AUTH] Code Verifier: {self.code_verifier[:20]}...')

            return auth_url

        except Exception as e:
            print(f'[AUTH] 生成授权URL失败: {e}')
            raise e

    def complete_oauth_flow(self, authorization_code):
        """完成OAuth流程"""
        try:
            # 解析授权码（可能是JSON格式）
            if authorization_code.startswith('{'):
                auth_data = json.loads(authorization_code)
                code = auth_data.get('code')
                state = auth_data.get('state', self.state)
                tenant_url = auth_data.get('tenant_url', '')
            else:
                code = authorization_code
                state = self.state
                tenant_url = ''

            print(f'[AUTH] 开始OAuth令牌交换，code: {code[:20]}...')

            # 选择令牌端点：对齐 headless-automation 的实现
            token_endpoint = self.token_url
            try:
                if tenant_url:
                    # headless-automation 使用 ${tenantUrl}token，没有 /oauth/ 路径
                    base = tenant_url if tenant_url.endswith('/') else tenant_url + '/'
                    token_endpoint = base + 'token'
            except Exception:
                pass
            print(f'[AUTH] Token Endpoint: {token_endpoint}')

            # 准备令牌请求（完全对齐 headless-automation 的字段顺序）
            token_data = {
                'grant_type': 'authorization_code',
                'client_id': self.client_id,
                'code_verifier': self.code_verifier,
                'redirect_uri': '',  # headless-automation 使用空字符串
                'code': code
            }

            headers = {
                'Content-Type': 'application/json',  # headless-automation 使用 JSON
                'Accept': 'application/json',
                'User-Agent': 'DrissionPage-Automation/1.0'
            }

            print(f'[AUTH] 请求数据: {json.dumps(token_data, indent=2)}')

            # 发送令牌请求（JSON 格式）
            response = requests.post(
                token_endpoint,
                json=token_data,  # 使用 json 参数而不是 data
                headers=headers,
                timeout=30
            )

            if response.status_code == 200:
                token_data = response.json()

                class TokenResponse:
                    def __init__(self, data, tenant_url=''):
                        self.access_token = data.get('access_token')
                        self.refresh_token = data.get('refresh_token')
                        self.expires_in = data.get('expires_in', 3600)
                        self.token_type = data.get('token_type', 'Bearer')
                        self.scope = data.get('scope', '')
                        self.tenant_url = tenant_url or 'https://augmentcode.com'

                print(f'[AUTH] OAuth令牌交换成功')
                return TokenResponse(token_data, tenant_url)
            else:
                print(f'[AUTH] OAuth令牌交换失败: {response.status_code} - {response.text}')
                raise Exception(f'令牌交换失败: {response.status_code}')

        except Exception as e:
            print(f'[AUTH] OAuth流程失败: {e}')
            # 不再返回 Mock，直接抛出异常（与 headless-automation 一致）
            raise e


class TokenStorage:
    """令牌存储处理器 - 纯 Python 实现"""

    def __init__(self):
        # 令牌存储文件路径
        self.tokens_file = Path(__file__).parent.parent / 'tokens.json'

    def add_token(self, token_response, metadata):
        """添加令牌（对齐 Node 的 tokens.json 结构和格式）"""
        try:
            # 生成 UUID 风格的 id（与 Node 保持一致）
            try:
                import uuid
                token_id = str(uuid.uuid4())
            except Exception:
                token_id = f"drissionpage_{int(time.time())}_{secrets.token_hex(8)}"

            # 生成时间字段：Node 使用 createdTime (ISO) 和 createdTimestamp (ms)
            now = datetime.utcnow()
            # 对齐 Node：将时间加 +8 小时后再写入 ISO（但时间戳保持实际 UTC 毫秒）
            utc8 = now + timedelta(hours=8)
            created_time_iso = utc8.isoformat(timespec='milliseconds') + 'Z'
            created_timestamp = int(now.timestamp() * 1000)

            # 组装与 Node 相同的字段结构
            token_data = {
                'id': token_id,
                'createdTime': created_time_iso,
                'createdTimestamp': created_timestamp,
                'access_token': getattr(token_response, 'access_token', ''),
                'tenant_url': getattr(token_response, 'tenant_url', ''),
                # Node 侧必有 oauth_state/parsed_code；Python侧若没有则留空/None 以兼容
                'oauth_state': getattr(token_response, 'oauth_state', None),
                'parsed_code': getattr(token_response, 'parsed_code', None),
                # 可选字段
                'portal_url': metadata.get('portal_url') if isinstance(metadata, dict) else None,
                'email_note': metadata.get('email') if isinstance(metadata, dict) else None,
                'description': metadata.get('description') if isinstance(metadata, dict) else 'DrissionPage token from Augment API via email verification',
                # 元数据
                'metadata': {
                    'user_agent': metadata.get('user_agent') if isinstance(metadata, dict) else 'drissionpage-email-verification',
                    'ip_address': metadata.get('ip_address') if isinstance(metadata, dict) else None,
                    'session_id': metadata.get('session_id') if isinstance(metadata, dict) else None,
                    'email': metadata.get('email') if isinstance(metadata, dict) else None,
                }
            }

            # 读取现有令牌
            tokens = []
            if self.tokens_file.exists():
                try:
                    with open(self.tokens_file, 'r', encoding='utf-8') as f:
                        tokens = json.load(f)
                except Exception:
                    tokens = []

            # 添加新令牌
            tokens.append(token_data)

            # 保存令牌文件（统一缩进为2、UTF-8）
            with open(self.tokens_file, 'w', encoding='utf-8') as f:
                json.dump(tokens, f, indent=2, ensure_ascii=False)

            print(f'[TOKEN] 令牌已保存: {token_id}')
            return token_id

        except Exception as e:
            print(f'[TOKEN] 保存令牌异常: {e}')
            return f"token_{int(time.time())}"


class OneMailHandler:
    """OneMail 邮箱处理器 - 纯 Python 实现"""

    def __init__(self):
        # OneMail API 配置
        self.api_url = os.getenv('ONE_MAIL_API_URL', 'https://one-mail.techexpresser.com/api/v1')
        self.api_password = os.getenv('API_AUTH_PASSWORD', 'your-api-password-here-123123123sdfsdsfd')

    def generate_email(self):
        """生成临时邮箱"""
        try:
            print('[EMAIL] 正在生成临时邮箱...')

            # 调用 OneMail API 生成邮箱 - 使用正确的认证头
            response = requests.post(
                f'{self.api_url}/generate-email',
                json={},
                headers={
                    'Authorization': f'Bearer {self.api_password}',
                    'Content-Type': 'application/json',
                    'User-Agent': 'DrissionPage-Automation/1.0'
                },
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('email'):
                    email = data.get('email')
                    print(f'[EMAIL] 临时邮箱生成成功: {email}')
                    return email
                else:
                    raise Exception('API返回格式错误')
            else:
                raise Exception(f'API请求失败: {response.status_code} - {response.text}')

        except Exception as e:
            print(f'[EMAIL] 生成邮箱失败: {e}')
            # 生成一个随机邮箱作为后备
            random_id = secrets.token_hex(8)
            fallback_email = f"test_{random_id}@1secmail.com"
            print(f'[EMAIL] 使用后备邮箱: {fallback_email}')
            return fallback_email

    def get_verification_code(self, email, timeout_minutes):
        """获取验证码（优先对齐 headless/real-browser 的 /verification-codes 接口，失败再回退 /get-emails）"""
        try:
            print(f'[EMAIL] 开始获取验证码，邮箱: {email}，超时: {timeout_minutes}分钟')

            start_time = time.time()
            timeout_seconds = timeout_minutes * 60
            check_interval = 5  # 每5秒检查一次

            while time.time() - start_time < timeout_seconds:
                elapsed = int(time.time() - start_time)
                try:
                    # 方式一：与 headless/real-browser 对齐的接口
                    resp = requests.get(
                        f'{self.api_url}/verification-codes',
                        params={
                            'email': email,
                            'latest': 'true',
                            'timeWindow': 5
                        },
                        headers={
                            'Authorization': f'Bearer {self.api_password}',
                            'User-Agent': 'DrissionPage-Automation/1.0'
                        },
                        timeout=10
                    )

                    if resp.status_code == 200:
                        data = resp.json()
                        if data.get('success') and data.get('latest_code'):
                            code_data = data['latest_code']
                            code = code_data.get('code')
                            if code:
                                print(f'[EMAIL] ✅ 找到验证码: {code} (subject={code_data.get("subject")}, date={code_data.get("date")})')
                                return code
                        else:
                            print('[EMAIL] 暂未收到验证码（/verification-codes）')
                    else:
                        print(f'[EMAIL] /verification-codes 调用失败: {resp.status_code} - {resp.text}')

                    # 方式二：回退到旧接口 /get-emails
                    response = requests.post(
                        f'{self.api_url}/get-emails',
                        json={'email': email},
                        headers={
                            'Authorization': f'Bearer {self.api_password}',
                            'Content-Type': 'application/json',
                            'User-Agent': 'DrissionPage-Automation/1.0'
                        },
                        timeout=10
                    )

                    if response.status_code == 200:
                        data = response.json()
                        emails = data.get('emails', [])
                        for email_data in emails:
                            subject = email_data.get('subject', '').lower()
                            body = email_data.get('body', '')
                            if any(keyword in subject for keyword in ['verification', 'code', '验证', '验证码']):
                                code = self._extract_verification_code(body)
                                if code:
                                    print(f'[EMAIL] ✅ 验证码获取成功(回退接口): {code}')
                                    return code
                    else:
                        print(f'[EMAIL] /get-emails 调用失败: {response.status_code} - {response.text}')

                    print(f'[EMAIL] 暂未收到验证码，继续等待... ({elapsed}s/{timeout_seconds}s)')
                    time.sleep(check_interval)

                except Exception as e:
                    print(f'[EMAIL] 检查邮件时出错: {e}')
                    time.sleep(check_interval)

            print('[EMAIL] 获取验证码超时')
            return None

        except Exception as e:
            print(f'[EMAIL] 获取验证码异常: {e}')
            return None

    def _extract_verification_code(self, email_body):
        """从邮件内容中提取验证码"""
        import re

        # 常见的验证码模式
        patterns = [
            r'verification code[:\s]*([0-9]{4,8})',
            r'code[:\s]*([0-9]{4,8})',
            r'验证码[：:\s]*([0-9]{4,8})',
            r'([0-9]{6})',  # 6位数字
            r'([0-9]{4})',  # 4位数字
        ]

        for pattern in patterns:
            match = re.search(pattern, email_body, re.IGNORECASE)
            if match:
                return match.group(1)

        return None
