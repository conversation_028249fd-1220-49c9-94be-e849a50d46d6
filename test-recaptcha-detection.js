const puppeteer = require('puppeteer');
const CaptchaHandler = require('./headless-automation/captcha.js');
const ProxyHandler = require('./headless-automation/proxy.js');
require('dotenv').config();

async function testRecaptchaDetection() {
    console.log('🔍 测试reCAPTCHA版本检测...');
    
    const browser = await puppeteer.launch({
        headless: false,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    const captchaHandler = new CaptchaHandler();
    
    try {
        // 测试URL
        const testUrl = process.env.LINK_TO_TEST || 'https://auth.augmentcode.com/authorize?response_type=code&code_challenge=gSUd4R2aDxmdnxVpc3s0ym_03yG0ZsjqEXQQkYxsyrE&client_id=v&state=IQcM4FQFq5s&prompt=login';
        
        console.log(`📱 访问测试页面: ${testUrl}`);
        await page.goto(testUrl, { waitUntil: 'networkidle2' });
        
        // 等待页面加载
        await page.waitForTimeout(3000);
        
        // 检测reCAPTCHA版本
        console.log('\n🔍 开始检测reCAPTCHA版本...');
        const recaptchaInfo = await captchaHandler.detectRecaptchaVersion(page);
        
        console.log('\n📊 检测结果总结:');
        console.log('='.repeat(50));
        console.log(`版本: ${recaptchaInfo.version}`);
        console.log(`Enterprise: ${recaptchaInfo.isEnterprise ? '是' : '否'}`);
        console.log(`Site Key: ${recaptchaInfo.siteKey || '未找到'}`);
        console.log(`v2支持: ${recaptchaInfo.hasV2 ? '是' : '否'}`);
        console.log(`v3支持: ${recaptchaInfo.hasV3 ? '是' : '否'}`);
        console.log('='.repeat(50));
        
        // 测试代理配置
        console.log('\n🌐 测试代理配置...');
        const proxyHandler = new ProxyHandler();
        const proxy = await proxyHandler.getValidProxy();
        
        if (proxy) {
            console.log(`✅ 代理配置: ${proxy.host}:${proxy.port}`);
        } else {
            console.log('🚫 未使用代理');
        }
        
        console.log('\n✅ 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    } finally {
        await browser.close();
    }
}

// 运行测试
if (require.main === module) {
    testRecaptchaDetection().catch(console.error);
}

module.exports = testRecaptchaDetection;
