# 邮箱验证自动化流程

这个项目实现了基于One Mail API的自动化邮箱验证流程，可以自动生成临时邮箱、填入邮箱地址、获取验证码并完成验证。

## 功能特点

- 🔄 自动生成临时邮箱（使用One Mail API）
- 📧 自动填入邮箱地址到表单
- ⏰ 自动获取验证码（2分钟超时，每5秒检查一次）
- 🔢 自动填入验证码并提交
- 📸 每一步都自动截图记录
- 💾 每一步都保存HTML内容用于调试
- 🛡️ 支持验证码识别和处理

## 环境配置

1. 确保One Mail API服务正在运行（默认端口9042）
2. 配置环境变量（.env文件）：

```bash
# One Mail API Configuration
API_AUTH_PASSWORD=your-api-password-here
ONE_MAIL_API_URL=http://localhost:9042/api/v1
```

## 安装依赖

```bash
npm install
```

## 使用方法

### 1. 测试One Mail API连接

```bash
npm run test-onemail
```

### 2. 运行邮箱验证流程

```bash
# 使用默认URL
npm run email-verification

# 使用自定义URL
node run-email-verification.js "https://your-target-url.com"
```

### 3. 运行完整的注册流程

```bash
npm start
```

## 流程说明

1. **启动浏览器** - 使用Puppeteer启动Chrome浏览器
2. **生成邮箱** - 调用One Mail API生成临时邮箱
3. **导航到页面** - 访问目标验证页面
4. **填入邮箱** - 自动查找邮箱输入框并填入生成的邮箱
5. **点击继续** - 自动点击Continue按钮或按Enter键
6. **等待验证码** - 每5秒调用API检查是否收到验证码
7. **填入验证码** - 自动查找验证码输入框并填入
8. **完成验证** - 点击最终的Continue按钮完成验证

## 调试功能

- 所有截图保存在 `image/` 目录
- 所有HTML内容保存在 `image/` 目录
- 文件命名格式：`STEP_XX_description.png` 和 `STEP_XX_description.html`
- 错误截图命名格式：`ERROR_XX_description.png`

## API接口

项目使用One Mail API的以下接口：

- `POST /api/v1/generate-email` - 生成临时邮箱
- `GET /api/v1/verification-codes` - 获取验证码

## 配置选项

可以在代码中调整以下参数：

- 验证码获取超时时间（默认2分钟）
- 检查间隔（默认5秒）
- 浏览器等待时间
- 输入框选择器

## 故障排除

1. **API连接失败**
   - 检查One Mail服务是否运行
   - 检查API_AUTH_PASSWORD是否正确
   - 检查防火墙设置

2. **找不到输入框**
   - 检查页面是否完全加载
   - 查看HTML文件确认页面结构
   - 可能需要调整选择器

3. **验证码获取超时**
   - 检查邮件是否发送成功
   - 增加超时时间
   - 检查邮件过滤规则

## 注意事项

- 确保One Mail API服务正常运行
- 某些网站可能有反自动化检测
- 验证码邮件可能有延迟
- 建议在测试环境中使用
