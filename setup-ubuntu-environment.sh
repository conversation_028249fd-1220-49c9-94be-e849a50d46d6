#!/bin/bash

# Ubuntu环境配置脚本
# 用于解决Ubuntu环境下被识别为机器人的问题

echo "🐧 Ubuntu环境抗指纹检测配置脚本"
echo "=================================="
echo ""

# 检查是否为root用户
if [ "$EUID" -eq 0 ]; then
    echo "⚠️  请不要使用root用户运行此脚本"
    echo "💡 请使用普通用户运行: bash setup-ubuntu-environment.sh"
    exit 1
fi

echo "📋 开始配置Ubuntu环境以避免机器人检测..."
echo ""

# 1. 更新系统包
echo "🔄 更新系统包..."
sudo apt-get update -qq

# 2. 安装Microsoft核心字体 - 最关键的步骤！
echo "🔤 安装Microsoft核心字体（最关键的步骤）..."
echo "   这将解决字体指纹检测问题"

# 预先接受许可协议
echo ttf-mscorefonts-installer msttcorefonts/accepted-mscorefonts-eula select true | sudo debconf-set-selections

# 安装字体包
sudo apt-get install -y ttf-mscorefonts-installer

# 安装额外的Windows字体
sudo apt-get install -y fonts-liberation fonts-dejavu-core fonts-dejavu-extra

# 3. 安装Chromium浏览器依赖
echo "🌐 安装Chromium浏览器依赖..."
sudo apt-get install -y \
    chromium-browser \
    fonts-liberation \
    libappindicator3-1 \
    libasound2 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libx11-xcb1 \
    libxcomposite1 \
    libxdamage1 \
    libxrandr2 \
    xdg-utils \
    libxss1 \
    libgconf-2-4

# 4. 安装额外的字体包来模拟Windows环境
echo "📝 安装额外字体包..."
sudo apt-get install -y \
    fonts-crosextra-carlito \
    fonts-crosextra-caladea \
    fonts-noto \
    fonts-opensymbol \
    fonts-symbola

# 5. 刷新字体缓存
echo "🔄 刷新字体缓存..."
sudo fc-cache -f -v > /dev/null 2>&1

# 6. 创建字体配置文件来优化字体渲染
echo "⚙️  配置字体渲染..."
mkdir -p ~/.config/fontconfig
cat > ~/.config/fontconfig/fonts.conf << 'EOF'
<?xml version="1.0"?>
<!DOCTYPE fontconfig SYSTEM "fonts.dtd">
<fontconfig>
  <!-- 优化字体渲染以模拟Windows -->
  <match target="font">
    <edit name="antialias" mode="assign">
      <bool>true</bool>
    </edit>
    <edit name="hinting" mode="assign">
      <bool>true</bool>
    </edit>
    <edit name="hintstyle" mode="assign">
      <const>hintslight</const>
    </edit>
    <edit name="rgba" mode="assign">
      <const>rgb</const>
    </edit>
  </match>
  
  <!-- 字体替换规则 -->
  <alias>
    <family>Arial</family>
    <prefer>
      <family>Liberation Sans</family>
    </prefer>
  </alias>
  <alias>
    <family>Times New Roman</family>
    <prefer>
      <family>Liberation Serif</family>
    </prefer>
  </alias>
  <alias>
    <family>Courier New</family>
    <prefer>
      <family>Liberation Mono</family>
    </prefer>
  </alias>
</fontconfig>
EOF

# 7. 设置环境变量
echo "🔧 配置环境变量..."
cat >> ~/.bashrc << 'EOF'

# 抗指纹检测环境变量
export DISPLAY=:99
export CHROME_DEVEL_SANDBOX=/usr/lib/chromium-browser/chrome-sandbox
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
export PUPPETEER_EXECUTABLE_PATH=/usr/bin/chromium-browser
EOF

# 8. 安装虚拟显示器（如果需要）
echo "🖥️  安装虚拟显示器支持..."
sudo apt-get install -y xvfb

# 9. 创建启动脚本
echo "📝 创建启动脚本..."
cat > run-with-display.sh << 'EOF'
#!/bin/bash
# 启动虚拟显示器并运行邮箱验证脚本

echo "🖥️  启动虚拟显示器..."
export DISPLAY=:99
Xvfb :99 -screen 0 1920x1080x24 > /dev/null 2>&1 &
XVFB_PID=$!

# 等待显示器启动
sleep 2

echo "🚀 运行邮箱验证脚本..."
node run-email-verification.js

# 清理
echo "🧹 清理虚拟显示器..."
kill $XVFB_PID > /dev/null 2>&1
EOF

chmod +x run-with-display.sh

# 10. 验证安装
echo ""
echo "✅ 配置完成！正在验证安装..."
echo ""

# 检查字体安装
echo "🔤 检查字体安装:"
fc-list | grep -i "arial\|times\|courier" | head -3
echo ""

# 检查Chromium
echo "🌐 检查Chromium安装:"
chromium-browser --version 2>/dev/null || echo "⚠️  Chromium未正确安装"
echo ""

echo "🎉 Ubuntu环境配置完成！"
echo ""
echo "📋 接下来的步骤:"
echo "1. 重新加载环境变量: source ~/.bashrc"
echo "2. 使用新的启动脚本: ./run-with-display.sh"
echo "3. 或者直接运行: node run-email-verification.js"
echo ""
echo "🔍 关键改进:"
echo "✅ 安装了Microsoft核心字体（解决字体指纹问题）"
echo "✅ 配置了字体渲染优化"
echo "✅ 安装了Chromium浏览器依赖"
echo "✅ 设置了虚拟显示器支持"
echo "✅ 应用了Ubuntu专用抗指纹配置"
echo ""
echo "💡 如果仍然被检测为机器人，请尝试:"
echo "   - 使用代理服务器"
echo "   - 降低操作频率"
echo "   - 检查网络环境"
echo ""
