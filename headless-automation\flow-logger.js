const fs = require('fs');
const path = require('path');

class FlowLogger {
    constructor() {
        this.logsDir = path.join(__dirname, 'logs');
        this.ensureLogsDirectory();
        this.currentSession = this.generateSessionId();
        this.singleLogFile = path.join(this.logsDir, 'flow.log');
    }

    // 确保logs目录存在
    ensureLogsDirectory() {
        if (!fs.existsSync(this.logsDir)) {
            fs.mkdirSync(this.logsDir, { recursive: true });
            console.log('📁 创建logs目录');
        }
    }

    // 生成会话ID
    generateSessionId() {
        const now = new Date();
        const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, -5);
        const random = Math.random().toString(36).substring(2, 8);
        return `${timestamp}_${random}`;
    }

    // 获取当前时间戳
    getTimestamp() {
        return new Date().toISOString();
    }

    // 记录流程开始
    logFlowStart() {
        const logData = {
            sessionId: this.currentSession,
            timestamp: this.getTimestamp(),
            event: 'FLOW_START'
        };

        this.appendLine(`START ${this.getTimestamp()}`);
        console.log(`📝 流程开始: ${this.currentSession}`);
    }

    // 记录流程成功
    logFlowSuccess(duration) {
        const logData = {
            sessionId: this.currentSession,
            timestamp: this.getTimestamp(),
            event: 'FLOW_SUCCESS',
            duration: duration
        };

        this.appendLine(`SUCCESS ${this.getTimestamp()} ${Math.round(duration/1000)}s`);
        console.log(`✅ 流程成功: ${this.currentSession} (耗时: ${Math.round(duration/1000)}秒)`);
    }

    // 记录流程失败
    logFlowFailure(error, duration) {
        const logData = {
            sessionId: this.currentSession,
            timestamp: this.getTimestamp(),
            event: 'FLOW_FAILURE',
            error: error.message,
            duration: duration
        };

        this.appendLine(`FAIL ${this.getTimestamp()} ${Math.round(duration/1000)}s`);
        console.log(`❌ 流程失败: ${this.currentSession} (耗时: ${Math.round(duration/1000)}秒) - ${error.message}`);
    }



    // 追加一行到 flow.log（纯文本）
    appendLine(line) {
        try {
            fs.appendFileSync(this.singleLogFile, line + '\n', 'utf8');
        } catch (error) {
            console.error(`写入日志失败: ${error.message}`);
        }
    }

    // 兼容旧接口（现在不再写入JSON摘要）
    writeSessionSummary() {
        // no-op
    }

    // 获取统计信息
    getStats() {
        try {
            const summaryFile = path.join(this.logsDir, 'session_summary.json');
            if (!fs.existsSync(summaryFile)) {
                return { total: 0, success: 0, failure: 0, successRate: 0 };
            }

            const content = fs.readFileSync(summaryFile, 'utf8');
            const summaries = JSON.parse(content);

            const total = summaries.length;
            const success = summaries.filter(s => s.status === 'SUCCESS').length;
            const failure = summaries.filter(s => s.status === 'FAILURE').length;
            const successRate = total > 0 ? Math.round((success / total) * 100) : 0;

            return { total, success, failure, successRate };
        } catch (error) {
            console.error(`获取统计失败: ${error.message}`);
            return { total: 0, success: 0, failure: 0, successRate: 0 };
        }
    }

    // 显示统计信息
    showStats() {
        const stats = this.getStats();
        console.log('');
        console.log('📊 流程执行统计:');
        console.log(`  总次数: ${stats.total}`);
        console.log(`  成功: ${stats.success} 次`);
        console.log(`  失败: ${stats.failure} 次`);
        console.log(`  成功率: ${stats.successRate}%`);
        console.log('');
    }

    // 清理旧日志（保留最近7天）
    cleanOldLogs() {
        try {
            const files = fs.readdirSync(this.logsDir);
            const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);

            files.forEach(file => {
                const filepath = path.join(this.logsDir, file);
                const stats = fs.statSync(filepath);
                
                if (stats.mtime.getTime() < sevenDaysAgo && file !== 'session_summary.json') {
                    fs.unlinkSync(filepath);
                    console.log(`🗑️ 清理旧日志: ${file}`);
                }
            });
        } catch (error) {
            console.error(`清理日志失败: ${error.message}`);
        }
    }
}

module.exports = FlowLogger;
