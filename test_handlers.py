#!/usr/bin/env python3
"""
测试处理器功能
"""
import sys
from pathlib import Path

# 添加 drissionpage-automation 目录到路径
sys.path.append(str(Path(__file__).parent / 'drissionpage-automation'))

def test_augment_auth():
    """测试 AugmentAuth"""
    print("🔐 测试 AugmentAuth...")
    
    try:
        from handlers import AugmentAuth
        
        auth = AugmentAuth()
        
        # 测试生成授权URL
        auth_url = auth.generate_auth_url()
        print(f"✅ 授权URL生成成功: {auth_url[:80]}...")
        
        # 验证URL格式
        if 'auth.augmentcode.com' in auth_url or 'https://' in auth_url:
            print("✅ 授权URL格式正确")
            return True
        else:
            print("❌ 授权URL格式错误")
            return False
            
    except Exception as e:
        print(f"❌ AugmentAuth 测试失败: {e}")
        return False

def test_onemail_handler():
    """测试 OneMailHandler"""
    print("\n📧 测试 OneMailHandler...")
    
    try:
        from handlers import OneMailHandler
        
        handler = OneMailHandler()
        
        # 测试生成邮箱
        email = handler.generate_email()
        print(f"✅ 邮箱生成成功: {email}")
        
        # 验证邮箱格式
        if '@' in email and '.' in email:
            print("✅ 邮箱格式正确")
            return True
        else:
            print("❌ 邮箱格式错误")
            return False
            
    except Exception as e:
        print(f"❌ OneMailHandler 测试失败: {e}")
        return False

def test_token_storage():
    """测试 TokenStorage"""
    print("\n💾 测试 TokenStorage...")
    
    try:
        from handlers import TokenStorage
        
        storage = TokenStorage()
        
        # 创建模拟令牌响应
        class MockTokenResponse:
            def __init__(self):
                self.access_token = "test_access_token"
                self.tenant_url = "https://test.com"
                self.refresh_token = "test_refresh_token"
                self.expires_in = 3600
                self.token_type = "Bearer"
                self.scope = "test"
        
        token_response = MockTokenResponse()
        metadata = {
            'description': 'Test token',
            'user_agent': 'test-agent',
            'session_id': 'test-session',
            'email': '<EMAIL>'
        }
        
        # 测试保存令牌
        token_id = storage.add_token(token_response, metadata)
        print(f"✅ 令牌保存成功: {token_id}")
        
        # 验证令牌ID格式
        if token_id and 'drissionpage_' in token_id:
            print("✅ 令牌ID格式正确")
            return True
        else:
            print("❌ 令牌ID格式错误")
            return False
            
    except Exception as e:
        print(f"❌ TokenStorage 测试失败: {e}")
        return False

def test_captcha_handler():
    """测试 CaptchaHandler"""
    print("\n🤖 测试 CaptchaHandler...")
    
    try:
        from handlers import CaptchaHandler
        
        handler = CaptchaHandler()
        
        # 测试初始化
        print("✅ CaptchaHandler 初始化成功")
        
        # 测试高分数模式
        handler.enable_high_score_mode()
        print("✅ 高分数模式启用成功")
        
        return True
            
    except Exception as e:
        print(f"❌ CaptchaHandler 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 DrissionPage 处理器功能测试")
    print("=" * 50)
    
    tests = [
        ("AugmentAuth", test_augment_auth),
        ("OneMailHandler", test_onemail_handler),
        ("TokenStorage", test_token_storage),
        ("CaptchaHandler", test_captcha_handler)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有处理器测试通过！")
        return True
    else:
        print("⚠️ 部分处理器测试失败。")
        return False

if __name__ == '__main__':
    success = main()
    import sys
    sys.exit(0 if success else 1)
