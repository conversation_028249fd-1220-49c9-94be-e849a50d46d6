const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * 令牌存储管理类
 * 管理 tokens.json 文件中的令牌数据
 */
class TokenStorage {
    constructor(filePath = null) {
        this.filePath = filePath || path.join(__dirname, '../tokens.json');
        this.ensureFileExists();
    }

    /**
     * 确保 tokens.json 文件存在
     */
    ensureFileExists() {
        try {
            if (!fs.existsSync(this.filePath)) {
                console.log('📁 创建新的令牌存储文件:', this.filePath);
                fs.writeFileSync(this.filePath, JSON.stringify([], null, 2), 'utf8');
            }
        } catch (error) {
            console.error('❌ 创建令牌文件失败:', error.message);
            throw error;
        }
    }

    /**
     * 生成 UUID
     * @returns {string} UUID 字符串
     */
    generateUUID() {
        return crypto.randomUUID();
    }

    /**
     * 读取所有令牌
     * @returns {Array} 令牌数组
     */
    getTokens() {
        try {
            const data = fs.readFileSync(this.filePath, 'utf8');
            const tokens = JSON.parse(data);
            console.log(`📖 读取到 ${tokens.length} 个令牌`);
            return tokens;
        } catch (error) {
            console.error('❌ 读取令牌文件失败:', error.message);
            return [];
        }
    }

    /**
     * 保存令牌数组到文件
     * @param {Array} tokens - 令牌数组
     */
    saveTokens(tokens) {
        try {
            fs.writeFileSync(this.filePath, JSON.stringify(tokens, null, 2), 'utf8');
            console.log(`💾 已保存 ${tokens.length} 个令牌到文件`);
        } catch (error) {
            console.error('❌ 保存令牌文件失败:', error.message);
            throw error;
        }
    }

    /**
     * 添加新令牌
     * @param {Object} tokenResponse - OAuth 流程的完整响应
     * @param {Object} options - 可选参数
     * @returns {string} 新令牌的 ID
     */
    addToken(tokenResponse, options = {}) {
        console.log('💾 添加新令牌到存储...');

        const tokens = this.getTokens();
        const tokenId = this.generateUUID();
        const now = new Date();
        // 转换为 UTC+8 时间
        const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));

        const newToken = {
            id: tokenId,
            createdTime: utc8Time.toISOString(),
            createdTimestamp: now.getTime(),
            access_token: tokenResponse.access_token,
            tenant_url: tokenResponse.tenant_url,
            oauth_state: tokenResponse.oauth_state,
            parsed_code: tokenResponse.parsed_code,
            // 可选字段
            portal_url: options.portal_url || null,
            email_note: options.email_note || null,
            description: options.description || null,
            // 元数据
            metadata: {
                user_agent: options.user_agent || 'augment-auto',
                ip_address: options.ip_address || null,
                session_id: options.session_id || null
            }
        };

        tokens.push(newToken);
        this.saveTokens(tokens);
        
        console.log('✅ 令牌添加成功:');
        console.log(`  - ID: ${tokenId}`);
        console.log(`  - 创建时间: ${newToken.createdTime}`);
        console.log(`  - 租户 URL: ${newToken.tenant_url}`);
        console.log(`  - 访问令牌: ${newToken.access_token.substring(0, 20)}...`);
        
        return tokenId;
    }

    /**
     * 根据 ID 获取令牌
     * @param {string} tokenId - 令牌 ID
     * @returns {Object|null} 令牌对象或 null
     */
    getTokenById(tokenId) {
        const tokens = this.getTokens();
        const token = tokens.find(t => t.id === tokenId);
        
        if (token) {
            console.log(`📖 找到令牌: ${tokenId}`);
        } else {
            console.log(`❌ 未找到令牌: ${tokenId}`);
        }
        
        return token || null;
    }

    /**
     * 更新令牌信息
     * @param {string} tokenId - 令牌 ID
     * @param {Object} updates - 要更新的字段
     * @returns {boolean} 是否更新成功
     */
    updateToken(tokenId, updates) {
        console.log(`🔄 更新令牌: ${tokenId}`);
        
        const tokens = this.getTokens();
        const tokenIndex = tokens.findIndex(t => t.id === tokenId);
        
        if (tokenIndex === -1) {
            console.log(`❌ 未找到要更新的令牌: ${tokenId}`);
            return false;
        }
        
        // 更新字段
        const now = new Date();
        const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
        tokens[tokenIndex] = {
            ...tokens[tokenIndex],
            ...updates,
            updatedTime: utc8Time.toISOString(),
            updatedTimestamp: Date.now()
        };
        
        this.saveTokens(tokens);
        console.log(`✅ 令牌更新成功: ${tokenId}`);
        return true;
    }

    /**
     * 删除令牌
     * @param {string} tokenId - 令牌 ID
     * @returns {boolean} 是否删除成功
     */
    deleteToken(tokenId) {
        console.log(`🗑️ 删除令牌: ${tokenId}`);
        
        const tokens = this.getTokens();
        const initialLength = tokens.length;
        const filteredTokens = tokens.filter(t => t.id !== tokenId);
        
        if (filteredTokens.length === initialLength) {
            console.log(`❌ 未找到要删除的令牌: ${tokenId}`);
            return false;
        }
        
        this.saveTokens(filteredTokens);
        console.log(`✅ 令牌删除成功: ${tokenId}`);
        return true;
    }

    /**
     * 获取令牌统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const tokens = this.getTokens();
        const now = Date.now();
        const oneDay = 24 * 60 * 60 * 1000;
        const oneWeek = 7 * oneDay;
        
        const stats = {
            total: tokens.length,
            today: tokens.filter(t => (now - t.createdTimestamp) < oneDay).length,
            thisWeek: tokens.filter(t => (now - t.createdTimestamp) < oneWeek).length,
            tenants: [...new Set(tokens.map(t => t.tenant_url))].length,
            oldestToken: tokens.length > 0 ? Math.min(...tokens.map(t => t.createdTimestamp)) : null,
            newestToken: tokens.length > 0 ? Math.max(...tokens.map(t => t.createdTimestamp)) : null
        };
        
        console.log('📊 令牌统计:');
        console.log(`  - 总数: ${stats.total}`);
        console.log(`  - 今日新增: ${stats.today}`);
        console.log(`  - 本周新增: ${stats.thisWeek}`);
        console.log(`  - 不同租户: ${stats.tenants}`);
        
        return stats;
    }

    /**
     * 清理过期或无效的令牌
     * @param {number} maxAge - 最大年龄（毫秒）
     * @returns {number} 清理的令牌数量
     */
    cleanup(maxAge = 30 * 24 * 60 * 60 * 1000) { // 默认30天
        console.log('🧹 开始清理过期令牌...');
        
        const tokens = this.getTokens();
        const now = Date.now();
        const validTokens = tokens.filter(t => (now - t.createdTimestamp) < maxAge);
        const removedCount = tokens.length - validTokens.length;
        
        if (removedCount > 0) {
            this.saveTokens(validTokens);
            console.log(`✅ 清理完成，删除了 ${removedCount} 个过期令牌`);
        } else {
            console.log('✅ 没有需要清理的令牌');
        }
        
        return removedCount;
    }

    /**
     * 导出令牌数据
     * @param {string} exportPath - 导出文件路径
     * @param {Object} options - 导出选项
     */
    exportTokens(exportPath, options = {}) {
        console.log(`📤 导出令牌数据到: ${exportPath}`);
        
        const tokens = this.getTokens();
        const now = new Date();
        const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));
        const exportData = {
            exportTime: utc8Time.toISOString(),
            exportTimestamp: Date.now(),
            version: '1.0',
            tokenCount: tokens.length,
            tokens: options.includeTokens ? tokens : tokens.map(t => ({
                id: t.id,
                createdTime: t.createdTime,
                tenant_url: t.tenant_url,
                description: t.description
            }))
        };
        
        fs.writeFileSync(exportPath, JSON.stringify(exportData, null, 2), 'utf8');
        console.log(`✅ 导出完成: ${tokens.length} 个令牌`);
    }
}

module.exports = TokenStorage;
