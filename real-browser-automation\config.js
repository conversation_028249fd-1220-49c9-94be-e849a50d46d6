const path = require('path');

// 加载根目录的 .env 文件
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * Real Browser Automation Configuration
 * 配置真实浏览器自动化的各种选项
 * 使用根目录的 .env 文件配置
 */
class RealBrowserConfig {
    constructor() {
        // 基础配置
        this.real_browser_proxy = process.env.REAL_BROWSER_PROXY === 'true' || false;
        this.real_browser_recaptcha_solve = process.env.REAL_BROWSER_RECAPTCHA_SOLVE === 'true' || false;

        // 代理配置 (复用现有的代理配置)
        this.proxy_url = process.env.PROXY_URL;
        this.proxy_user = process.env.PROXY_USER;
        this.proxy_pass = process.env.PROXY_PASS;

        // YesCaptcha 配置
        this.yescaptcha_client_key = process.env.YESCAPTCHA_CLIENT_KEY;
        
        // 调试配置
        this.debug_mode = process.env.DEBUG_MODE === 'true' || true;
        this.save_screenshots = process.env.SAVE_SCREENSHOTS === 'true' || true;
        this.save_html = process.env.SAVE_HTML === 'true' || true;
        
        // 超时配置
        this.page_timeout = parseInt(process.env.PAGE_TIMEOUT) || 30000;
        this.email_check_timeout = parseInt(process.env.EMAIL_CHECK_TIMEOUT) || 120000;
        this.email_check_interval = parseInt(process.env.EMAIL_CHECK_INTERVAL) || 5000;
        
        // 验证配置
        this.validate();
    }
    
    validate() {
        if (this.real_browser_proxy && (!this.proxy_url || !this.proxy_user || !this.proxy_pass)) {
            console.warn('⚠️ 警告: 启用了代理但代理配置不完整');
            console.warn('💡 请在 .env 文件中设置: PROXY_URL, PROXY_USER, PROXY_PASS');
        }
        
        if (this.real_browser_recaptcha_solve && !this.yescaptcha_client_key) {
            console.warn('⚠️ 警告: 启用了验证码解决但未设置 YesCaptcha 密钥');
            console.warn('💡 请在 .env 文件中设置: YESCAPTCHA_CLIENT_KEY');
        }
    }
    
    getProxyConfig() {
        if (!this.real_browser_proxy || !this.proxy_url) {
            return null;
        }
        
        return {
            host: this.proxy_url.split(':')[0],
            port: parseInt(this.proxy_url.split(':')[1]),
            username: this.proxy_user,
            password: this.proxy_pass
        };
    }
    
    printConfig() {
        console.log('🔧 Real Browser 配置:');
        console.log(`   📡 代理: ${this.real_browser_proxy ? '启用' : '禁用'}`);
        if (this.real_browser_proxy && this.proxy_url) {
            console.log(`   🌐 代理地址: ${this.proxy_url}`);
            console.log(`   👤 代理用户: ${this.proxy_user}`);
        }
        console.log(`   🤖 验证码解决: ${this.real_browser_recaptcha_solve ? '启用' : '禁用'}`);
        console.log(`   📸 截图保存: ${this.save_screenshots ? '启用' : '禁用'}`);
        console.log(`   📄 HTML保存: ${this.save_html ? '启用' : '禁用'}`);
        console.log(`   ⏱️ 页面超时: ${this.page_timeout}ms`);
        console.log(`   📧 邮箱检查超时: ${this.email_check_timeout}ms`);
    }
}

module.exports = RealBrowserConfig;
