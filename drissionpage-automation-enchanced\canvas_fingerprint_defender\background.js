
// Basic Fingerprint Defender Background Script
console.log('🛡️ Basic Fingerprint Defender loaded');

// 监听页面加载
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    if (changeInfo.status === 'loading' && tab.url) {
        // 注入防护脚本
        chrome.scripting.executeScript({
            target: { tabId: tabId },
            func: injectFingerprintProtection
        }).catch(err => {
            console.log('Script injection failed:', err);
        });
    }
});

function injectFingerprintProtection() {
    // 基本的指纹防护
    try {
        // 移除 webdriver 属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });
        
        // 伪装 Chrome 对象
        if (!window.chrome) {
            window.chrome = {
                runtime: {
                    onConnect: undefined,
                    onMessage: undefined
                }
            };
        }
        
        console.log('🛡️ Basic fingerprint protection injected');
    } catch (e) {
        console.log('Fingerprint protection error:', e);
    }
}
