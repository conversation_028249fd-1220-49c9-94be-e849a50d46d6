const axios = require('axios');
require('dotenv').config();

class ProxyHandler {
    constructor(config = {}) {
        // 支持多种代理配置
        this.config = {
            // 用户现有代理配置 (从环境变量读取)
            current: {
                host: config.current?.host || (process.env.PROXY_URL ? process.env.PROXY_URL.split(':')[0] : null),
                port: config.current?.port || (process.env.PROXY_URL ? parseInt(process.env.PROXY_URL.split(':')[1]) : null),
                username: config.current?.username || process.env.PROXY_USER,
                password: config.current?.password || process.env.PROXY_PASS,
                sessionDuration: 10 * 60 * 1000, // 10分钟会话
                protocol: config.current?.protocol || 'http' // 支持 http 或 socks5
            },
            // 住宅代理配置 (推荐)
            residential: {
                endpoint: config.residential?.endpoint || 'rotating-residential.oxylabs.io:8001',
                username: config.residential?.username || process.env.RESIDENTIAL_PROXY_USER,
                password: config.residential?.password || process.env.RESIDENTIAL_PROXY_PASS,
                sessionDuration: 10 * 60 * 1000, // 10分钟会话
                country: config.residential?.country || 'US'
            },
            // 花生代理配置 (备用)
            peanut: {
                url: config.peanut?.url || 'https://getip.我使用的是花生的代理，可以自己让gpt适配其他的http代理文件删了也能跑.com/servers.php?session=--4d927ef1177e15a911a7dda24408d11c&time=3&count=1&type=text&only=1&pw=no&protocol=http&separator=1&iptype=tunnel&format=null&dev=web',
                sessionDuration: 3 * 60 * 1000 // 3分钟会话
            }
        };

        this.proxyType = config.type || 'current'; // 'current', 'residential' 或 'peanut'
        this.currentProxy = null;
        this.proxyExpireTime = null;
        this.sessionId = this.generateSessionId();
    }

    // 生成会话ID
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    }

    // 获取新的代理
    async getProxy() {
        if (this.proxyType === 'current') {
            return this.getCurrentProxy();
        } else if (this.proxyType === 'residential') {
            return this.getResidentialProxy();
        } else {
            return this.getPeanutProxy();
        }
    }

    // 获取用户当前代理 (从环境变量)
    async getCurrentProxy() {
        try {
            const config = this.config.current;

            // 检查必要的配置
            if (!config.host || !config.port) {
                console.log('[PROXY] ❌ 代理配置不完整，请检查 PROXY_URL 环境变量');
                return null;
            }

            // 检查当前代理是否还有效
            const sessionDuration = config.sessionDuration;
            if (this.currentProxy && this.proxyExpireTime && Date.now() < this.proxyExpireTime) {
                console.log(`[PROXY] 使用缓存代理: ${config.host}:${config.port}`);
                return this.currentProxy;
            }

            console.log('[PROXY] 配置用户代理...');

            this.currentProxy = {
                host: config.host,
                port: config.port,
                username: config.username,
                password: config.password,
                protocol: config.protocol,
                type: 'current'
            };

            // 设置会话过期时间
            this.proxyExpireTime = Date.now() + sessionDuration;

            console.log(`[PROXY] ✅ 用户代理配置成功: ${config.host}:${config.port}`);
            console.log(`[PROXY] 协议: ${config.protocol}, 用户: ${config.username}`);

            return this.currentProxy;

        } catch (error) {
            console.log(`[PROXY] ❌ 获取用户代理失败: ${error.message}`);
            return null;
        }
    }

    // 获取住宅代理
    async getResidentialProxy() {
        try {
            // 检查当前代理是否还有效
            const sessionDuration = this.config.residential.sessionDuration;
            if (this.currentProxy && this.proxyExpireTime && Date.now() < this.proxyExpireTime) {
                console.log(`[PROXY] 使用缓存住宅代理会话: ${this.sessionId}`);
                return this.formatResidentialProxy();
            }

            console.log('[PROXY] 创建新的住宅代理会话...');

            // 生成新的会话ID
            this.sessionId = this.generateSessionId();

            // 住宅代理通常使用固定端点+认证的方式
            const config = this.config.residential;
            this.currentProxy = {
                host: config.endpoint.split(':')[0],
                port: parseInt(config.endpoint.split(':')[1]),
                username: `${config.username}-session-${this.sessionId}-country-${config.country}`,
                password: config.password,
                type: 'residential'
            };

            // 设置会话过期时间
            this.proxyExpireTime = Date.now() + sessionDuration;

            console.log(`[PROXY] ✅ 住宅代理会话创建成功: ${this.sessionId}`);
            console.log(`[PROXY] 代理端点: ${config.endpoint}, 国家: ${config.country}`);

            return this.formatResidentialProxy();

        } catch (error) {
            console.log(`[PROXY] ❌ 获取住宅代理失败: ${error.message}`);
            return null;
        }
    }

    // 获取花生代理 (原有逻辑)
    async getPeanutProxy() {
        try {
            // 检查当前代理是否还有效
            const sessionDuration = this.config.peanut.sessionDuration;
            if (this.currentProxy && this.proxyExpireTime && Date.now() < this.proxyExpireTime) {
                console.log(`[PROXY] 使用缓存花生代理: ${this.currentProxy.host}:${this.currentProxy.port}`);
                return this.currentProxy;
            }

            console.log('[PROXY] 获取新花生代理...');

            const response = await axios.get(this.config.peanut.url, {
                timeout: 15000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });

            if (response.data && typeof response.data === 'string') {
                const proxyData = response.data.trim();

                // 验证代理格式 (IP:PORT)
                if (this.isValidProxy(proxyData)) {
                    const [host, port] = proxyData.split(':');
                    this.currentProxy = {
                        host,
                        port: parseInt(port),
                        type: 'peanut'
                    };
                    // 设置过期时间
                    this.proxyExpireTime = Date.now() + sessionDuration;
                    console.log(`[PROXY] ✅ 花生代理获取成功: ${proxyData}`);
                    return this.currentProxy;
                } else {
                    console.log(`[PROXY] ❌ 代理格式无效: ${proxyData}`);
                    return null;
                }
            } else {
                console.log('[PROXY] ❌ 代理响应格式错误');
                return null;
            }

        } catch (error) {
            console.log(`[PROXY] ❌ 获取花生代理失败: ${error.message}`);
            return null;
        }
    }

    // 格式化住宅代理返回格式
    formatResidentialProxy() {
        if (!this.currentProxy) return null;

        return {
            host: this.currentProxy.host,
            port: this.currentProxy.port,
            username: this.currentProxy.username,
            password: this.currentProxy.password,
            type: this.currentProxy.type
        };
    }

    // 验证代理格式
    isValidProxy(proxy) {
        // 检查格式是否为 IP:PORT
        const proxyRegex = /^(\d{1,3}\.){3}\d{1,3}:\d{1,5}$/;
        return proxyRegex.test(proxy);
    }

    // 测试代理连通性
    async testProxy(proxyConfig) {
        try {
            const proxyInfo = proxyConfig.username ?
                `${proxyConfig.host}:${proxyConfig.port} (${proxyConfig.type})` :
                `${proxyConfig.host}:${proxyConfig.port}`;

            console.log(`[PROXY] 测试代理连通性: ${proxyInfo}`);

            const axiosConfig = {
                timeout: 15000,
                proxy: {
                    host: proxyConfig.host,
                    port: proxyConfig.port,
                    protocol: 'http'
                }
            };

            // 如果有认证信息，添加认证
            if (proxyConfig.username && proxyConfig.password) {
                axiosConfig.proxy.auth = {
                    username: proxyConfig.username,
                    password: proxyConfig.password
                };
            }

            const response = await axios.get('http://httpbin.org/ip', axiosConfig);

            if (response.status === 200) {
                console.log(`[PROXY] ✅ 代理测试成功: ${proxyInfo}`);
                console.log(`[PROXY] 出口IP: ${response.data.origin}`);

                // 如果是住宅代理，显示会话信息
                if (proxyConfig.type === 'residential') {
                    console.log(`[PROXY] 会话ID: ${this.sessionId}`);
                }

                return true;
            } else {
                console.log(`[PROXY] ❌ 代理测试失败: ${proxyInfo}`);
                return false;
            }

        } catch (error) {
            console.log(`[PROXY] ❌ 代理测试失败: ${proxyInfo} - ${error.message}`);
            return false;
        }
    }

    // 获取并测试代理
    async getValidProxy() {
        // 检查HEADLESS_PROXY环境变量
        const useProxy = process.env.HEADLESS_PROXY === 'true';

        if (!useProxy) {
            console.log('[PROXY] 🚫 HEADLESS_PROXY=false，跳过代理配置');
            return null;
        }

        console.log('[PROXY] ✅ HEADLESS_PROXY=true，启用代理配置');

        let attempts = 0;
        const maxAttempts = 3;

        while (attempts < maxAttempts) {
            attempts++;

            const proxy = await this.getProxy();
            if (!proxy) {
                console.log(`[PROXY] 获取代理失败，尝试 ${attempts}/${maxAttempts}`);
                await this.wait(2000);
                continue;
            }

            // 测试代理连通性
            const isValid = await this.testProxy(proxy);
            if (isValid) {
                return proxy;
            } else {
                console.log(`[PROXY] 代理无效，重新获取... ${attempts}/${maxAttempts}`);
                this.clearProxy();
                await this.wait(2000);
            }
        }

        console.log('[PROXY] ❌ 无法获取有效代理，将不使用代理');
        return null;
    }

    // 强制刷新代理（用于切换身份）
    async refreshProxy() {
        console.log('[PROXY] 强制刷新代理...');
        this.clearProxy();
        return await this.getValidProxy();
    }

    // 清除当前代理缓存
    clearProxy() {
        this.currentProxy = null;
        this.proxyExpireTime = null;
        console.log('[PROXY] 代理缓存已清除');
    }

    // 获取代理状态信息
    getProxyStatus() {
        if (!this.currentProxy) {
            return {
                hasProxy: false,
                proxy: null,
                timeLeft: 0,
                type: this.proxyType,
                sessionId: null
            };
        }

        const timeLeft = this.proxyExpireTime ? Math.max(0, this.proxyExpireTime - Date.now()) : 0;

        return {
            hasProxy: true,
            proxy: this.currentProxy,
            timeLeft: Math.floor(timeLeft / 1000), // 秒
            expired: timeLeft <= 0,
            type: this.proxyType,
            sessionId: this.sessionId
        };
    }

    // 获取代理配置用于浏览器
    getBrowserProxyConfig() {
        if (!this.currentProxy) {
            return null;
        }

        // 根据协议类型构建代理服务器地址
        const protocol = this.currentProxy.protocol || 'http';
        const config = {
            server: `${protocol}://${this.currentProxy.host}:${this.currentProxy.port}`
        };

        // 如果有认证信息，添加认证
        if (this.currentProxy.username && this.currentProxy.password) {
            config.username = this.currentProxy.username;
            config.password = this.currentProxy.password;
        }

        return config;
    }

    // 获取 Puppeteer 代理参数
    getPuppeteerProxyArgs() {
        if (!this.currentProxy) {
            return [];
        }

        const protocol = this.currentProxy.protocol || 'http';
        const proxyServer = `${protocol}://${this.currentProxy.host}:${this.currentProxy.port}`;

        return [`--proxy-server=${proxyServer}`];
    }

    // 检查当前IP信息 (使用代理配置)
    async checkCurrentIP() {
        try {
            // 检查是否启用代理
            const useProxy = process.env.HEADLESS_PROXY === 'true';
            let axiosConfig = {
                timeout: 10000
            };

            // 如果启用代理，使用代理配置
            if (useProxy && this.currentProxy) {
                axiosConfig.proxy = {
                    host: this.currentProxy.host,
                    port: this.currentProxy.port,
                    protocol: 'http'
                };

                // 如果有认证信息，添加认证
                if (this.currentProxy.username && this.currentProxy.password) {
                    axiosConfig.proxy.auth = {
                        username: this.currentProxy.username,
                        password: this.currentProxy.password
                    };
                }
                console.log(`[PROXY] 🌐 通过代理检查IP: ${this.currentProxy.host}:${this.currentProxy.port}`);
            } else {
                console.log(`[PROXY] 🌐 直接检查IP (未使用代理)`);
            }

            const response = await axios.get('https://httpbin.org/ip', axiosConfig);
            const ip = response.data.origin;
            console.log(`[PROXY] 🌐 当前IP: ${ip}`);

            // 获取IP详细信息
            try {
                // 使用不同的IP信息服务，避免代理问题
                let ipInfoResponse;
                try {
                    ipInfoResponse = await axios.get(`http://ip-api.com/json/${ip}`, {
                        ...axiosConfig,
                        timeout: 10000
                    });
                } catch (error) {
                    // 如果第一个服务失败，尝试第二个
                    ipInfoResponse = await axios.get(`https://ipapi.co/${ip}/json/`, {
                        ...axiosConfig,
                        timeout: 10000
                    });
                }
                const ipInfo = ipInfoResponse.data;

                // 处理不同API的响应格式
                const country = ipInfo.country || ipInfo.country_name;
                const countryCode = ipInfo.countryCode || ipInfo.country_code;
                const city = ipInfo.city;
                const isp = ipInfo.isp || ipInfo.org;
                const type = ipInfo.type || 'unknown';

                console.log(`[PROXY] 📍 IP信息:`);
                console.log(`[PROXY]   - 国家: ${country} (${countryCode})`);
                console.log(`[PROXY]   - 城市: ${city}`);
                console.log(`[PROXY]   - ISP: ${isp}`);
                console.log(`[PROXY]   - 类型: ${type}`);

                return {
                    ip: ip,
                    country: country,
                    city: city,
                    isp: isp,
                    type: type
                };
            } catch (error) {
                console.log(`[PROXY] ⚠️ 无法获取IP详细信息: ${error.message}`);
                return { ip: ip };
            }
        } catch (error) {
            console.log(`[PROXY] ❌ 无法检查IP: ${error.message}`);
            return null;
        }
    }

    // 辅助方法
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = ProxyHandler;

