# Real Browser Automation

使用 `puppeteer-real-browser` 进行真实浏览器自动化的邮箱验证流程。

## 功能特点

- ✅ 使用真实浏览器（非 headless）进行自动化
- ✅ 支持代理配置
- ✅ 支持自动验证码解决（YesCaptcha）
- ✅ 每一步都自动截图和保存HTML
- ✅ 详细的日志记录
- ✅ 完整的错误处理和调试信息

## 配置选项

### 主要配置

- `REAL_BROWSER_PROXY`: 是否启用代理 (true/false)
- `REAL_BROWSER_RECAPTCHA_SOLVE`: 是否启用验证码自动解决 (true/false)

### 代理配置

当 `REAL_BROWSER_PROXY=true` 时需要配置：
- `PROXY_URL`: 代理服务器地址
- `PROXY_USER`: 代理用户名
- `PROXY_PASS`: 代理密码

### 验证码配置

当 `REAL_BROWSER_RECAPTCHA_SOLVE=true` 时需要配置：
- `YESCAPTCHA_CLIENT_KEY`: YesCaptcha 客户端密钥

## 安装和使用

### 1. 安装依赖

```bash
npm install puppeteer-real-browser
```

### 2. 配置环境变量

在根目录的 `.env` 文件中配置相应的值。Real Browser Automation 使用与 headless-automation 相同的配置文件。

主要配置项：
```env
REAL_BROWSER_PROXY=false
REAL_BROWSER_RECAPTCHA_SOLVE=false
PROXY_URL=your_proxy_host:port
PROXY_USER=your_username
PROXY_PASS=your_password
YESCAPTCHA_CLIENT_KEY=your_yescaptcha_key
```

### 3. 运行自动化

```bash
node run-real-browser-verification.js
```

## 文件结构

```
real-browser-automation/
├── config.js                          # 配置管理
├── real-browser-logger.js             # 日志记录器
├── real-browser-automation.js         # 主要自动化类
├── run-real-browser-verification.js   # 运行脚本
├── .env.example                       # 配置示例
├── README.md                          # 说明文档
├── screenshots/                       # 截图目录
├── html/                              # HTML保存目录
└── logs/                              # 日志目录
```

## 流程说明

1. **生成授权URL** - 生成 Augment 授权链接
2. **生成临时邮箱** - 使用 OneMailAPI 生成临时邮箱
3. **启动真实浏览器** - 启动可见的浏览器窗口
4. **导航到授权页面** - 打开授权链接
5. **处理验证码** - 自动或手动处理 reCAPTCHA
6. **输入邮箱** - 输入临时邮箱地址
7. **点击继续** - 提交邮箱信息
8. **等待验证页面** - 等待验证码输入页面
9. **获取验证码** - 从邮箱获取验证码
10. **输入验证码** - 输入获取到的验证码
11. **点击最终继续** - 完成验证
12. **等待授权码页面** - 等待授权码显示
13. **提取授权码** - 从页面或剪贴板提取授权码
14. **完成OAuth流程** - 调用 Augment API 获取令牌
15. **保存令牌** - 保存到 tokens.json

## 调试功能

### 自动记录

每一步都会自动保存：
- 📸 **截图**: `screenshots/STEP_XX_stepname.png`
- 📄 **HTML**: `html/STEP_XX_stepname.html`
- 📋 **日志**: `logs/real-browser-YYYY-MM-DD.log`

### 错误记录

发生错误时会保存：
- 📸 **错误截图**: `screenshots/ERROR_XX_errorname.png`
- 📄 **错误HTML**: `html/ERROR_XX_errorname.html`

## 配置示例

### 启用代理和验证码解决

```env
REAL_BROWSER_PROXY=true
REAL_BROWSER_RECAPTCHA_SOLVE=true
PROXY_URL=us2.cliproxy.io:3010
PROXY_USER=lovh89107-region-SG
PROXY_PASS=your_password
YESCAPTCHA_CLIENT_KEY=your_key
```

### 仅启用代理

```env
REAL_BROWSER_PROXY=true
REAL_BROWSER_RECAPTCHA_SOLVE=false
PROXY_URL=us2.cliproxy.io:3010
PROXY_USER=lovh89107-region-SG
PROXY_PASS=your_password
```

### 仅启用验证码解决

```env
REAL_BROWSER_PROXY=false
REAL_BROWSER_RECAPTCHA_SOLVE=true
YESCAPTCHA_CLIENT_KEY=your_key
```

### 完全手动模式

```env
REAL_BROWSER_PROXY=false
REAL_BROWSER_RECAPTCHA_SOLVE=false
```

## 注意事项

1. **真实浏览器**: 此版本使用真实浏览器窗口，您可以看到整个自动化过程
2. **手动干预**: 如果禁用自动验证码解决，程序会暂停等待您手动处理验证码
3. **代理认证**: 如果使用代理，确保代理服务器支持用户名/密码认证
4. **资源清理**: 程序结束时会自动关闭浏览器和页面

## 故障排除

1. **浏览器启动失败**: 检查是否安装了 Chrome/Chromium
2. **代理连接失败**: 检查代理配置和网络连接
3. **验证码解决失败**: 检查 YesCaptcha 余额和配置
4. **邮箱验证码获取失败**: 检查 OneMailAPI 配置和网络

查看 `logs/` 目录中的详细日志文件获取更多调试信息。
