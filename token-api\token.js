const axios = require('axios');

class TokenHandler {
    constructor() {
        this.authToken = null;
    }

    // 获取auth token
    async getAuthToken(page, baseUrl) {
        try {
            console.log('[TOKEN] 获取auth token...');
            
            await page.goto(baseUrl, { 
                waitUntil: 'domcontentloaded', 
                timeout: 30000 
            });
            
            await this.wait(2000);
            
            const passwordSelectors = [
                'input[type="password"]',
                'input[name="password"]',
                'input[placeholder*="密码" i]',
                'input[name*="pass"]'
            ];
            
            const passwordInput = await this.findInputField(page, passwordSelectors);
            if (!passwordInput) {
                throw new Error('未找到密码输入框');
            }
            
            await passwordInput.click();
            await passwordInput.type('123456请使用你augment2api的密码', { delay: 80 });
            
            const loginClicked = await this.clickButtonContaining(page, ['登录', 'login', '确定']);
            if (!loginClicked) {
                await page.keyboard.press('Enter');
            }
            
            await this.wait(4000);
            
            const cookies = await page.cookies();
            const authCookie = cookies.find(cookie => 
                cookie.name === 'auth_token' || 
                cookie.name === 'auth' || 
                cookie.name.includes('auth')
            );
            
            if (authCookie) {
                this.authToken = authCookie.value;
                console.log('[TOKEN] ✅ auth token获取成功');
                return this.authToken;
            }
            
            throw new Error('无法获取auth token');
            
        } catch (error) {
            console.log(`[TOKEN] ❌ 获取auth token失败: ${error.message}`);
            throw error;
        }
    }

    // 获取授权URL
    async getAuthUrl(baseUrl, authToken) {
        let attempts = 0;
        const maxAttempts = 2;
        
        while (attempts < maxAttempts) {
            try {
                if (!authToken) {
                    throw new Error('auth token未设置');
                }
                
                const response = await axios.get(`${baseUrl}/auth`, {
                    headers: {
                        'Cookie': `auth_token=${authToken}`,
                        'x-auth-token': authToken,
                        'User-Agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36'
                    },
                    timeout: 20000
                });
                
                if (response.data && response.data.authorize_url) {
                    console.log('[TOKEN] ✅ 获取授权URL成功');
                    return response.data.authorize_url;
                } else {
                    throw new Error('授权URL响应格式错误');
                }
                
            } catch (error) {
                attempts++;
                console.log(`[TOKEN] 获取授权URL失败 (${attempts}/${maxAttempts}): ${error.message}`);
                
                if (attempts >= maxAttempts) {
                    throw new Error(`获取授权URL失败: ${error.message}`);
                }
                await this.wait(2000);
            }
        }
    }

    // 提交回调数据
    async submitCallback(baseUrl, authToken, jsonData) {
        try {
            const response = await axios.post(`${baseUrl}/callback`, jsonData, {
                headers: {
                    'Cookie': `auth_token=${authToken}`,
                    'x-auth-token': authToken,
                    'content-type': 'application/json',
                    'User-Agent': 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36'
                },
                timeout: 20000
            });
            
            console.log('[TOKEN] ✅ 回调提交成功');
            return response.data;
        } catch (error) {
            console.log(`[TOKEN] ❌ 回调提交失败: ${error.message}`);
            throw error;
        }
    }

    // 辅助方法
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async findInputField(page, selectors) {
        for (const selector of selectors) {
            try {
                await page.waitForSelector(selector, { timeout: 5000 });
                const input = await page.$(selector);
                if (input) {
                    return input;
                }
            } catch (e) { 
                continue; 
            }
        }
        return null;
    }

    async clickButtonContaining(page, keywords, timeout = 15000) {
        try {
            await page.waitForSelector('button, a, input[type="submit"], [role="button"]', { timeout });
            
            const elements = await page.$$('button, a, input[type="submit"], [role="button"]');
            
            for (const element of elements) {
                const text = await page.evaluate(el => {
                    return (el.textContent || el.value || el.getAttribute('aria-label') || '').trim();
                }, element);
                
                for (const keyword of keywords) {
                    if (text.toLowerCase().includes(keyword.toLowerCase())) {
                        await element.click();
                        await this.wait(1000);
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }
}

module.exports = TokenHandler;

