# 住宅代理配置 (推荐用于反自动化检测)
# 示例：Oxylabs 住宅代理
RESIDENTIAL_PROXY_USER=your_username
RESIDENTIAL_PROXY_PASS=your_password
RESIDENTIAL_PROXY_ENDPOINT=rotating-residential.oxylabs.io:8001
RESIDENTIAL_PROXY_COUNTRY=US

# 示例：Smartproxy 住宅代理
# RESIDENTIAL_PROXY_USER=your_username
# RESIDENTIAL_PROXY_PASS=your_password  
# RESIDENTIAL_PROXY_ENDPOINT=gate.smartproxy.com:10000
# RESIDENTIAL_PROXY_COUNTRY=US

# 示例：Bright Data 住宅代理
# RESIDENTIAL_PROXY_USER=your_username-session-rand123
# RESIDENTIAL_PROXY_PASS=your_password
# RESIDENTIAL_PROXY_ENDPOINT=zproxy.lum-superproxy.io:22225
# RESIDENTIAL_PROXY_COUNTRY=US

# 其他配置
LINK_TO_TEST=https://example.com
