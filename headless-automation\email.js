const axios = require('axios');

class EmailHandler {
    constructor() {
        this.tempEmail = null;
        this.baseUrl = 'https://45mb.com';
        this.lastEmailId = null;
    }

    log(message) {
        console.log(`[EMAIL] ${new Date().toLocaleTimeString()} - ${message}`);
    }

    async generateTempEmail() {
        try {
            // 生成8位随机数字
            const randomNum = Math.floor(10000000 + Math.random() * 90000000);
            this.tempEmail = `apek${randomNum}@45mb.com`;
            this.log(`✅ 临时邮箱生成成功: ${this.tempEmail}`);
            return this.tempEmail;
            
        } catch (error) {
            this.log(`❌ 邮箱生成失败: ${error.message}`);
            throw error;
        }
    }

    async getCurrentEmail() {
        return this.tempEmail;
    }

    async clearCurrentEmail() {
        this.tempEmail = null;
        this.lastEmailId = null;
        this.log("当前邮箱信息已清除");
    }

    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    async checkEmailForCode() {
        if (!this.tempEmail) {
            throw new Error("请先生成临时邮箱");
        }
        
        try {
            const response = await axios.get(`${this.baseUrl}/api.php?email=${this.tempEmail}`, {
                timeout: 10000,
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            });
            
            if (response.data && response.data.verification_code) {
                const subject = response.data.subject || '';
                const code = response.data.verification_code;
                
                // 检查邮件主题是否包含 augment
                if (subject.toLowerCase().includes('augment')) {
                    this.log(`✅ 找到符合条件的邮件: ${subject}`);
                    this.log(`验证码: ${code}`);
                    return code;
                }
            }
            
            // 如果API返回的verification_code为空，尝试从body中提取
            if (response.data && response.data.body) {
                const subject = response.data.subject || '';
                const body = response.data.body || '';
                
                if (subject.toLowerCase().includes('augment')) {
                    // 从邮件正文中提取6位数字验证码
                    const codeMatch = body.match(/\b\d{6}\b/);
                    if (codeMatch) {
                        this.log(`✅ 从邮件正文提取验证码: ${codeMatch[0]}`);
                        return codeMatch[0];
                    }
                }
            }
            
            return null;
            
        } catch (error) {
            this.log(`邮件检查失败: ${error.message}`);
            return null;
        }
    }

    async waitForVerificationCode(maxWaitTime = 180000, interval = 8000) {
        if (!this.tempEmail) {
            throw new Error("请先生成临时邮箱");
        }
        
        this.log(`开始等待验证码，邮箱: ${this.tempEmail}`);
        const startTime = Date.now();
        let attempt = 0;
        
        while (Date.now() - startTime < maxWaitTime) {
            attempt++;
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            
            if (attempt % 5 === 1) {
                this.log(`检查邮件 (#${attempt}) - 已等待 ${elapsed}秒...`);
            }
            
            const code = await this.checkEmailForCode();
            if (code) {
                this.log(`✅ 成功获取验证码: ${code}`);
                return code;
            }
            
            await this.wait(interval);
        }
        
        this.log("❌ 验证码获取超时");
        throw new Error("验证码获取超时");
    }

    // 独立测试方法
    async testEmailReceiving() {
        try {
            console.log("=== 45mb.com 临时邮箱收件测试 ===");
            
            // 生成测试邮箱
            const testEmail = await this.generateTempEmail();
            console.log(`测试邮箱: ${testEmail}`);
            console.log(`API地址: ${this.baseUrl}/api.php?email=${testEmail}`);
            
            // 等待用户发送测试邮件
            const readline = require('readline');
            const rl = readline.createInterface({
                input: process.stdin,
                output: process.stdout
            });
            
            rl.question("请发送包含 'Augment' 的邮件到上述邮箱，然后按回车键开始检查...", async () => {
                rl.close();
                
                console.log("开始检查邮件...");
                const startTime = Date.now();
                let found = false;
                
                while (!found && Date.now() - startTime < 120000) {
                    try {
                        const response = await axios.get(`${this.baseUrl}/api.php?email=${testEmail}`, {
                            timeout: 10000
                        });
                        
                        console.log("API响应:", JSON.stringify(response.data, null, 2));
                        
                        const code = await this.checkEmailForCode();
                        if (code) {
                            console.log(`✅ 找到验证码: ${code}`);
                            found = true;
                            break;
                        }
                        
                        console.log("未找到符合条件的邮件，10秒后重试...");
                        await this.wait(10000);
                        
                    } catch (error) {
                        console.log(`检查邮件出错: ${error.message}`);
                        await this.wait(10000);
                    }
                }
                
                if (!found) {
                    console.log("❌ 未找到符合条件的邮件");
                }
                
                process.exit();
            });
            
        } catch (error) {
            console.error("测试失败:", error.message);
            process.exit(1);
        }
    }
}

// 独立运行测试
if (require.main === module) {
    const emailHandler = new EmailHandler();
    emailHandler.testEmailReceiving();
}

module.exports = EmailHandler;

