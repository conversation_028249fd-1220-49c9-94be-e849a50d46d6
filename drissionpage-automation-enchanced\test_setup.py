#!/usr/bin/env python3
"""
DrissionPage Automation Setup Test
测试 DrissionPage 自动化环境是否正确配置
"""

import sys
import os
from pathlib import Path

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        from DrissionPage import ChromiumPage, ChromiumOptions
        print("✅ DrissionPage 导入成功")
    except ImportError as e:
        print(f"❌ DrissionPage 导入失败: {e}")
        return False
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        return False
    
    try:
        from dotenv import load_dotenv
        print("✅ python-dotenv 导入成功")
    except ImportError as e:
        print(f"❌ python-dotenv 导入失败: {e}")
        return False
    
    try:
        import pyperclip
        print("✅ pyperclip 导入成功")
    except ImportError as e:
        print(f"❌ pyperclip 导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n🔧 测试配置...")
    
    try:
        from config import DrissionPageConfig
        config = DrissionPageConfig()
        print("✅ 配置类初始化成功")
        
        # 打印配置信息
        config.print_config()
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_logger():
    """测试日志记录器"""
    print("\n📋 测试日志记录器...")
    
    try:
        from drissionpage_logger import DrissionPageLogger
        logger = DrissionPageLogger()
        logger.log("测试日志消息")
        print("✅ 日志记录器测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 日志记录器测试失败: {e}")
        return False

def test_handlers():
    """测试处理器"""
    print("\n🔧 测试处理器...")
    
    try:
        from handlers import CaptchaHandler, AugmentAuth, TokenStorage, OneMailHandler
        
        captcha_handler = CaptchaHandler()
        print("✅ CaptchaHandler 初始化成功")
        
        augment_auth = AugmentAuth()
        print("✅ AugmentAuth 初始化成功")
        
        token_storage = TokenStorage()
        print("✅ TokenStorage 初始化成功")
        
        onemail_handler = OneMailHandler()
        print("✅ OneMailHandler 初始化成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        return False

def test_browser():
    """测试浏览器启动"""
    print("\n🌐 测试浏览器启动...")

    try:
        from DrissionPage import ChromiumPage, ChromiumOptions

        # 创建浏览器选项 - 使用正确的 DrissionPage API
        options = ChromiumOptions()
        options.headless()  # 无头模式测试
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-dev-shm-usage')

        # 创建页面
        page = ChromiumPage(addr_or_opts=options)

        # 测试导航
        page.get('https://www.google.com')
        title = page.title

        # 关闭浏览器
        page.quit()

        print(f"✅ 浏览器测试成功，访问页面标题: {title}")
        return True

    except Exception as e:
        print(f"❌ 浏览器测试失败: {e}")
        print("💡 请确保已安装 Chrome/Chromium 浏览器")
        return False

def test_automation_class():
    """测试自动化类"""
    print("\n🤖 测试自动化类...")
    
    try:
        from drissionpage_automation import DrissionPageAutomation
        
        automation = DrissionPageAutomation()
        print("✅ DrissionPageAutomation 初始化成功")
        
        # 测试配置
        automation.config.print_config()
        
        return True
        
    except Exception as e:
        print(f"❌ 自动化类测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 DrissionPage Automation 环境测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置", test_config),
        ("日志记录器", test_logger),
        ("处理器", test_handlers),
        ("浏览器", test_browser),
        ("自动化类", test_automation_class)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！环境配置正确。")
        print("💡 现在可以运行: python run_drissionpage_verification.py")
        return True
    else:
        print("⚠️ 部分测试失败，请检查环境配置。")
        print("💡 请确保已安装所有依赖: pip install -r requirements.txt")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
