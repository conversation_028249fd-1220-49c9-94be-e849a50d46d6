const fs = require('fs');
const path = require('path');

function viewLogs() {
    const logsDir = path.join(__dirname, 'logs');
    
    if (!fs.existsSync(logsDir)) {
        console.log('📁 logs 目录不存在');
        return;
    }

    const summaryFile = path.join(logsDir, 'session_summary.json');
    
    if (!fs.existsSync(summaryFile)) {
        console.log('📄 暂无日志记录');
        return;
    }

    try {
        const content = fs.readFileSync(summaryFile, 'utf8');
        const summaries = JSON.parse(content);

        console.log('📊 邮箱验证流程日志');
        console.log('═'.repeat(60));
        console.log('');

        // 显示统计
        const total = summaries.length;
        const success = summaries.filter(s => s.status === 'SUCCESS').length;
        const failure = summaries.filter(s => s.status === 'FAILURE').length;
        const successRate = total > 0 ? Math.round((success / total) * 100) : 0;

        console.log(`📈 总体统计:`);
        console.log(`  总次数: ${total}`);
        console.log(`  成功: ${success} 次`);
        console.log(`  失败: ${failure} 次`);
        console.log(`  成功率: ${successRate}%`);
        console.log('');

        // 显示最近10条记录
        console.log('📋 最近10条记录:');
        console.log('─'.repeat(60));
        
        const recent = summaries.slice(-10).reverse();
        
        recent.forEach((record, index) => {
            const time = new Date(record.timestamp).toLocaleString('zh-CN');
            const status = record.status === 'SUCCESS' ? '✅ 成功' : '❌ 失败';
            const duration = record.duration ? `${Math.round(record.duration/1000)}秒` : '未知';
            
            console.log(`${index + 1}. ${time}`);
            console.log(`   状态: ${status}`);
            console.log(`   耗时: ${duration}`);
            
            if (record.error) {
                console.log(`   错误: ${record.error}`);
            }
            
            console.log(`   会话: ${record.sessionId}`);
            console.log('');
        });

        // 显示今日统计
        const today = new Date().toDateString();
        const todayRecords = summaries.filter(s => 
            new Date(s.timestamp).toDateString() === today
        );
        
        if (todayRecords.length > 0) {
            const todaySuccess = todayRecords.filter(s => s.status === 'SUCCESS').length;
            const todayFailure = todayRecords.filter(s => s.status === 'FAILURE').length;
            const todayRate = Math.round((todaySuccess / todayRecords.length) * 100);
            
            console.log('📅 今日统计:');
            console.log(`  今日总数: ${todayRecords.length}`);
            console.log(`  今日成功: ${todaySuccess} 次`);
            console.log(`  今日失败: ${todayFailure} 次`);
            console.log(`  今日成功率: ${todayRate}%`);
        }

    } catch (error) {
        console.error('读取日志失败:', error.message);
    }
}

if (require.main === module) {
    viewLogs();
}

module.exports = { viewLogs };
