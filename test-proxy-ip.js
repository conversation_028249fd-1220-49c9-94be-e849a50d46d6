const ProxyHandler = require('./headless-automation/proxy.js');

async function testProxyIP() {
    console.log('🔍 测试代理IP配置...');
    console.log('='.repeat(50));
    
    try {
        // 创建代理处理器
        const proxyHandler = new ProxyHandler();
        
        // 显示当前配置
        console.log('📋 当前配置:');
        console.log(`   HEADLESS_PROXY: ${process.env.HEADLESS_PROXY}`);
        console.log(`   PROXY_URL: ${process.env.PROXY_URL}`);
        console.log(`   PROXY_USER: ${process.env.PROXY_USER}`);
        console.log('');
        
        // 获取代理配置
        console.log('🔧 获取代理配置...');
        const proxy = await proxyHandler.getValidProxy();
        
        if (proxy) {
            console.log(`✅ 代理配置成功: ${proxy.host}:${proxy.port}`);
            console.log(`🔐 代理用户: ${proxy.username}`);
            console.log(`📡 代理协议: ${proxy.protocol || 'http'}`);
        } else {
            console.log('❌ 未获取到代理配置');
        }
        
        console.log('');
        console.log('🌐 检查IP信息...');
        
        // 检查IP信息（现在会使用代理）
        const ipInfo = await proxyHandler.checkCurrentIP();
        
        if (ipInfo) {
            console.log('');
            console.log('📊 IP分析结果:');
            console.log('='.repeat(30));
            console.log(`🌐 IP地址: ${ipInfo.ip}`);
            console.log(`🏳️ 国家: ${ipInfo.country}`);
            console.log(`🏙️ 城市: ${ipInfo.city}`);
            console.log(`🏢 ISP: ${ipInfo.isp}`);
            console.log(`📋 类型: ${ipInfo.type || 'unknown'}`);
            console.log('='.repeat(30));
            
            // 分析结果
            if (proxy) {
                console.log('');
                console.log('🔍 代理效果分析:');
                if (ipInfo.country && ipInfo.country.toLowerCase().includes('thailand')) {
                    console.log('✅ 代理工作正常 - 显示泰国IP');
                } else if (ipInfo.country && ipInfo.country.toLowerCase().includes('singapore')) {
                    console.log('✅ 代理工作正常 - 显示新加坡IP');
                } else {
                    console.log(`⚠️ 代理可能未生效 - 显示${ipInfo.country}IP`);
                    console.log('💡 建议检查代理配置或联系代理服务商');
                }
            }
        } else {
            console.log('❌ 无法获取IP信息');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    }
    
    console.log('');
    console.log('✅ 测试完成！');
}

// 运行测试
testProxyIP();
