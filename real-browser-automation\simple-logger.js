const fs = require('fs');
const path = require('path');

class SimpleLogger {
    constructor() {
        this.logsDir = path.join(__dirname, 'logs');
        this.ensureLogsDir();
    }

    ensureLogsDir() {
        if (!fs.existsSync(this.logsDir)) {
            fs.mkdirSync(this.logsDir, { recursive: true });
        }
    }

    getUTC8Time() {
        const now = new Date();
        // 转换为 UTC+8 时间
        const utc8Time = new Date(now.getTime() + (8 * 60 * 60 * 1000));

        // 格式化为 "09 Jul 2025 08:30 pm" 格式
        const options = {
            day: '2-digit',
            month: 'short',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
            timeZone: 'UTC'
        };

        return utc8Time.toLocaleString('en-GB', options).replace(',', '');
    }

    getLogFileName() {
        const now = new Date();
        const utc8Date = new Date(now.getTime() + (8 * 60 * 60 * 1000));
        const dateStr = utc8Date.toISOString().split('T')[0];
        return `automation-${dateStr}.log`;
    }

    writeLog(message) {
        const logFile = path.join(this.logsDir, this.getLogFileName());
        const timestamp = this.getUTC8Time();
        const logEntry = `[${timestamp}] ${message}\n`;
        
        fs.appendFileSync(logFile, logEntry, 'utf8');
        console.log(logEntry.trim());
    }

    logStart(email) {
        const timestamp = this.getUTC8Time();
        this.writeLog(`${email} ${timestamp} start`);
    }

    logSuccess(email) {
        const timestamp = this.getUTC8Time();
        this.writeLog(`${email} ${timestamp} success`);
    }

    logFailure(email, error) {
        const timestamp = this.getUTC8Time();
        this.writeLog(`${email} ${timestamp} failed - ${error}`);
    }
}

module.exports = SimpleLogger;
