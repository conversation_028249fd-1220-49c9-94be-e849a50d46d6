#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Token API Server...\n');

// Start the token API server
const apiProcess = spawn('node', ['token-api.js'], {
  cwd: __dirname,
  stdio: 'inherit'
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down Token API Server...');
  apiProcess.kill('SIGINT');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down Token API Server...');
  apiProcess.kill('SIGTERM');
  process.exit(0);
});

apiProcess.on('close', (code) => {
  console.log(`\n📊 Token API Server exited with code ${code}`);
  process.exit(code);
});

apiProcess.on('error', (error) => {
  console.error('❌ Failed to start Token API Server:', error);
  process.exit(1);
});
