const crypto = require('crypto');
const readline = require('readline');
const axios = require('axios');

/**
 * 生成OAuth 2.0 PKCE授权链接
 * 
 * OAuth 2.0 PKCE (Proof Key for Code Exchange) 流程说明：
 * 1. 客户端生成一个随机的 code_verifier
 * 2. 对 code_verifier 进行 SHA256 哈希并 base64url 编码得到 code_challenge
 * 3. 将用户重定向到授权服务器，携带 code_challenge
 * 4. 用户授权后，授权服务器返回 authorization_code
 * 5. 客户端使用 authorization_code 和原始的 code_verifier 换取 access_token
 */

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @returns {string} 随机字符串
 */
function generateRandomString(length) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    let result = '';
    const randomBytes = crypto.randomBytes(length);
    
    for (let i = 0; i < length; i++) {
        result += charset[randomBytes[i] % charset.length];
    }
    
    return result;
}

/**
 * Base64URL 编码
 * @param {Buffer} buffer - 要编码的数据
 * @returns {string} Base64URL编码的字符串
 */
function base64URLEncode(buffer) {
    return buffer
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}

/**
 * 生成 PKCE code challenge
 * @param {string} codeVerifier - code verifier
 * @returns {string} code challenge
 */
function generateCodeChallenge(codeVerifier) {
    const hash = crypto.createHash('sha256').update(codeVerifier).digest();
    return base64URLEncode(hash);
}

/**
 * 生成OAuth授权链接
 * @returns {object} 包含授权链接和相关参数的对象
 */
function generateOAuthLink() {
    // 1. 生成 code_verifier (43-128个字符)
    const codeVerifier = generateRandomString(64);
    
    // 2. 生成 code_challenge
    const codeChallenge = generateCodeChallenge(codeVerifier);
    
    // 3. 生成随机 state (防止CSRF攻击)
    const state = generateRandomString(10);
    
    // 4. 构建授权URL的参数
    const authParams = {
        response_type: 'code',           // 使用授权码流程
        code_challenge: codeChallenge,   // PKCE challenge
        client_id: 'v',                  // 客户端ID
        state: state,                    // 防CSRF状态值
        prompt: 'login'                  // 强制显示登录页面
    };
    
    // 5. 构建完整的授权URL
    const baseUrl = 'https://auth.augmentcode.com/authorize';
    const queryString = Object.entries(authParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');
    
    const authUrl = `${baseUrl}?${queryString}`;
    
    return {
        authUrl,
        codeVerifier,  // 需要保存，后续换取token时使用
        codeChallenge,
        state,
        authParams
    };
}

/**
 * 等待用户输入
 * @param {string} prompt - 提示信息
 * @returns {Promise<string>} 用户输入的内容
 */
function waitForUserInput(prompt) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(prompt, (answer) => {
            rl.close();
            resolve(answer.trim());
        });
    });
}

/**
 * 解析授权码
 * @param {string} codeInput - 授权码 JSON 字符串
 * @returns {Object} 解析后的授权码对象
 */
function parseAuthCode(codeInput) {
    console.log('\n📋 解析授权码...');

    try {
        const parsed = JSON.parse(codeInput);
        console.log('✅ 授权码解析成功:');
        console.log(`  - Code: ${parsed.code?.substring(0, 20)}...`);
        console.log(`  - State: ${parsed.state}`);
        console.log(`  - Tenant URL: ${parsed.tenant_url}`);

        return parsed;
    } catch (error) {
        console.error('❌ 授权码解析失败:', error.message);
        throw new Error(`Failed to parse authorization code: ${error.message}`);
    }
}

/**
 * 获取访问令牌
 * @param {string} tenantUrl - 租户 URL
 * @param {string} codeVerifier - 代码验证器
 * @param {string} code - 授权码
 * @returns {Promise<string>} 访问令牌
 */
async function getAccessToken(tenantUrl, codeVerifier, code) {
    console.log('\n🔑 获取访问令牌...');
    console.log(`  - Tenant URL: ${tenantUrl}`);
    console.log(`  - Code: ${code.substring(0, 20)}...`);
    console.log(`  - Code Verifier: ${codeVerifier.substring(0, 20)}...`);

    const tokenUrl = `${tenantUrl}token`;
    const postData = {
        grant_type: 'authorization_code',
        client_id: 'v',
        code_verifier: codeVerifier,
        redirect_uri: '',
        code: code
    };

    console.log(`📡 发送 API 请求到: ${tokenUrl}`);

    try {
        const response = await axios.post(tokenUrl, postData, {
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'augment-oauth-client/1.0'
            },
            timeout: 30000
        });

        if (response.data && response.data.access_token) {
            console.log('✅ 访问令牌获取成功!');
            return response.data.access_token;
        } else {
            throw new Error('Invalid token response format');
        }
    } catch (error) {
        console.error('❌ 获取访问令牌失败:', error.message);
        if (error.response) {
            console.error('API 响应:', error.response.status, error.response.data);
        }
        throw error;
    }
}

/**
 * 主函数 - 生成授权链接并处理完整流程
 */
async function main() {
    console.log('🔐 OAuth 2.0 PKCE 授权链接生成器');
    console.log('=' .repeat(50));

    // 步骤1: 生成授权链接
    const result = generateOAuthLink();

    console.log('\n📋 生成的授权链接:');
    console.log(result.authUrl);

    console.log('\n🔧 生成参数详情:');
    console.log(`Code Verifier: ${result.codeVerifier}`);
    console.log(`Code Challenge: ${result.codeChallenge}`);
    console.log(`State: ${result.state}`);

    console.log('\n📖 使用说明:');
    console.log('1. 复制上面的授权链接到浏览器中打开');
    console.log('2. 完成授权流程');
    console.log('3. 从剪贴板复制授权响应的 JSON 数据');
    console.log('4. 粘贴到下面的输入框中');

    // 步骤2: 等待用户输入授权码
    console.log('\n⏳ 等待您输入授权码...');
    const authCodeInput = await waitForUserInput('\n📋 请粘贴从剪贴板复制的授权码 JSON: ');

    if (!authCodeInput) {
        console.log('❌ 未输入授权码，程序退出');
        return;
    }

    try {
        // 步骤3: 解析授权码
        const parsedCode = parseAuthCode(authCodeInput);

        // 步骤4: 验证 state
        if (parsedCode.state !== result.state) {
            throw new Error('State mismatch. Possible CSRF attack.');
        }
        console.log('✅ State 验证通过');

        // 步骤5: 获取访问令牌
        const accessToken = await getAccessToken(
            parsedCode.tenant_url,
            result.codeVerifier,
            parsedCode.code
        );

        // 步骤6: 显示结果
        console.log('\n🎉 OAuth 流程完成！');
        console.log('=' .repeat(50));
        console.log(`🔑 访问令牌: ${accessToken.substring(0, 30)}...`);
        console.log(`🏢 租户 URL: ${parsedCode.tenant_url}`);
        console.log(`📊 令牌长度: ${accessToken.length} 字符`);

        // 保存令牌信息
        const tokenInfo = {
            access_token: accessToken,
            tenant_url: parsedCode.tenant_url,
            created_at: new Date().toISOString(),
            oauth_state: result,
            parsed_code: parsedCode
        };

        console.log('\n💾 令牌信息已准备就绪');
        console.log('您可以将此令牌用于 Augment API 调用');

        return tokenInfo;

    } catch (error) {
        console.error('\n❌ 处理授权码失败:', error.message);
        console.error('请检查输入的授权码格式是否正确');
        return null;
    }
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('程序执行失败:', error.message);
        process.exit(1);
    });
}

// 导出函数供其他模块使用
module.exports = {
    generateOAuthLink,
    generateCodeChallenge,
    generateRandomString,
    base64URLEncode,
    parseAuthCode,
    getAccessToken,
    waitForUserInput
};
