/**
 * 抗指纹检测配置模块
 * 用于增强Puppeteer浏览器的隐蔽性，避免被检测为自动化工具
 * 集成Ubuntu环境特殊优化
 */

const crypto = require('crypto');
const UbuntuAntiFingerprint = require('./ubuntu-anti-fingerprint.js');

/**
 * 生成随机数值
 */
function randomBetween(min, max) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 生成随机字符串
 */
function randomString(length) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

/**
 * 获取随机User-Agent
 */
function getRandomUserAgent() {
    const userAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36'
    ];
    return userAgents[Math.floor(Math.random() * userAgents.length)];
}

/**
 * 获取增强的浏览器启动参数
 * 自动检测Ubuntu环境并应用相应优化
 */
function getEnhancedLaunchOptions() {
    // 如果是Ubuntu环境，使用专门的优化配置
    if (UbuntuAntiFingerprint.isUbuntuEnvironment()) {
        console.log('🐧 检测到Ubuntu环境，应用Ubuntu专用抗指纹配置');
        return UbuntuAntiFingerprint.getUbuntuEnhancedLaunchOptions();
    }

    // Windows/其他环境的原有配置
    return {
        headless: true, // 强制使用 headless 模式
        args: [
            // 基础安全参数（保留）
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',

            // 抗自动化（保留）
            '--disable-blink-features=AutomationControlled',

            // 收敛禁用项，避免环境异常
            // '--disable-web-security',             // 移除
            // '--disable-features=VizDisplayCompositor',
            // '--disable-extensions-http-throttling',
            // '--disable-ipc-flooding-protection',
            // '--disable-renderer-backgrounding',
            // '--disable-backgrounding-occluded-windows',
            // '--disable-background-timer-throttling',
            // '--disable-features=TranslateUI',
            // '--disable-default-apps',
            // '--disable-sync',
            // '--disable-translate',
            '--hide-scrollbars',
            '--mute-audio',
            '--no-first-run',
            '--no-default-browser-check',
            '--no-pings',
            '--password-store=basic',
            '--use-mock-keychain',

            // 不再禁用 WebGL/RTC/图片等
            // '--disable-plugins',
            // '--disable-extensions',
            // '--disable-images',
            // '--disable-javascript-harmony-shipping',
            // '--disable-client-side-phishing-detection',
            // '--disable-component-update',
            // '--disable-domain-reliability',
            // '--disable-features=AudioServiceOutOfProcess',
            // '--disable-features=VizDisplayCompositor',
            // '--disable-logging',
            // '--disable-notifications',
            // '--disable-permissions-api',
            // '--disable-speech-api',
            // '--disable-web-bluetooth',
            // '--disable-webgl',
            // '--disable-webrtc',

            // 内存和性能优化（保留）
            '--memory-pressure-off',
            '--max_old_space_size=4096',

            // 网络相关（收敛）
            '--aggressive-cache-discard',
            '--disable-background-networking',

            // 随机窗口大小
            `--window-size=${randomBetween(1200, 1920)},${randomBetween(800, 1080)}`,

            // 语言设置（与SG一致）
            '--lang=en-SG,en',
            '--accept-lang=en-SG,en;q=0.9'
        ],
        defaultViewport: {
            width: randomBetween(1200, 1920),
            height: randomBetween(800, 1080),
            deviceScaleFactor: 1,
            isMobile: false,
            hasTouch: false,
            isLandscape: true
        },
        timeout: 120000,
        ignoreDefaultArgs: ['--enable-automation', '--enable-blink-features=IdleDetection'],
        ignoreHTTPSErrors: true
    };
}

/**
 * 应用抗指纹检测脚本到页面
 * 自动检测Ubuntu环境并应用相应优化
 * 增强版本，专门针对 Verisoul 检测
 */
async function applyAntiFingerprinting(page) {
    // 如果是Ubuntu环境，使用专门的抗指纹脚本
    if (UbuntuAntiFingerprint.isUbuntuEnvironment()) {
        console.log('🐧 应用Ubuntu专用抗指纹脚本');
        await UbuntuAntiFingerprint.applyUbuntuAntiFingerprinting(page);
        return;
    }

    // Windows/其他环境的原有配置
    // 设置随机User-Agent
    await page.setUserAgent(getRandomUserAgent());

    // 设置额外的HTTP头
    await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-SG,en;q=0.9',
        'Cache-Control': 'max-age=0',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    });

    // 在每个新文档加载时执行抗指纹脚本
    await page.evaluateOnNewDocument(() => {
        // 隐藏webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });

        // 伪装chrome对象
        window.chrome = {
            runtime: {
                onConnect: undefined,
                onMessage: undefined
            },
            loadTimes: function () {
                return {
                    commitLoadTime: Date.now() / 1000 - Math.random() * 100,
                    connectionInfo: 'http/1.1',
                    finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 10,
                    finishLoadTime: Date.now() / 1000 - Math.random() * 10,
                    firstPaintAfterLoadTime: 0,
                    firstPaintTime: Date.now() / 1000 - Math.random() * 10,
                    navigationType: 'Other',
                    npnNegotiatedProtocol: 'unknown',
                    requestTime: Date.now() / 1000 - Math.random() * 100,
                    startLoadTime: Date.now() / 1000 - Math.random() * 100,
                    wasAlternateProtocolAvailable: false,
                    wasFetchedViaSpdy: false,
                    wasNpnNegotiated: false
                };
            },
            csi: function () {
                return {
                    pageT: Date.now() - Math.random() * 1000,
                    startE: Date.now() - Math.random() * 1000,
                    tran: Math.floor(Math.random() * 20)
                };
            }
        };

        // 伪装语言设置
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
            configurable: true
        });

        // 伪装插件
        Object.defineProperty(navigator, 'plugins', {
            get: () => [
                {
                    0: { type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format" },
                    description: "Portable Document Format",
                    filename: "internal-pdf-viewer",
                    length: 1,
                    name: "Chrome PDF Plugin"
                },
                {
                    0: { type: "application/pdf", suffixes: "pdf", description: "" },
                    description: "",
                    filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                    length: 1,
                    name: "Chrome PDF Viewer"
                }
            ],
            configurable: true
        });

        // 伪装权限API
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: Notification.permission }) :
                originalQuery(parameters)
        );

        // 伪装硬件并发数
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => Math.floor(Math.random() * 8) + 4,
            configurable: true
        });

        // 伪装设备内存
        Object.defineProperty(navigator, 'deviceMemory', {
            get: () => [2, 4, 8][Math.floor(Math.random() * 3)],
            configurable: true
        });

        // 伪装平台
        Object.defineProperty(navigator, 'platform', {
            get: () => 'Win32',
            configurable: true
        });

        // 移除自动化相关属性
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

        // 伪装WebGL指纹
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function (parameter) {
            if (parameter === 37445) {
                return 'Intel Inc.';
            }
            if (parameter === 37446) {
                return 'Intel(R) Iris(TM) Graphics 6100';
            }
            return getParameter(parameter);
        };

        // 增强的Canvas指纹伪装
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;

        HTMLCanvasElement.prototype.toDataURL = function () {
            const context = this.getContext('2d');
            if (context) {
                // 添加一致但微小的噪声
                const noise = Math.sin(this.width * this.height) * 0.0001;
                context.fillStyle = `rgba(${Math.floor(noise * 255)}, ${Math.floor(noise * 255)}, ${Math.floor(noise * 255)}, 0.01)`;
                context.fillRect(0, 0, 1, 1);
            }
            return originalToDataURL.apply(this, arguments);
        };

        CanvasRenderingContext2D.prototype.getImageData = function () {
            const imageData = originalGetImageData.apply(this, arguments);
            // 添加微小但一致的噪声
            const noise = Math.sin(imageData.width * imageData.height) * 0.01;
            for (let i = 0; i < imageData.data.length; i += 4) {
                imageData.data[i] = Math.min(255, Math.max(0, imageData.data[i] + noise));
            }
            return imageData;
        };

        // 伪装屏幕分辨率
        Object.defineProperty(screen, 'width', {
            get: () => 1920 + Math.floor(Math.random() * 100),
            configurable: true
        });
        Object.defineProperty(screen, 'height', {
            get: () => 1080 + Math.floor(Math.random() * 100),
            configurable: true
        });

        // 伪装时区（与新加坡代理一致）
        Date.prototype.getTimezoneOffset = function () {
            return -480; // UTC+8 (Singapore)
        };

        // 伪装AudioContext指纹
        if (window.AudioContext || window.webkitAudioContext) {
            const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
            const audioContexts = [];

            function PatchedAudioContext() {
                const context = new OriginalAudioContext();
                audioContexts.push(context);

                // 伪装音频指纹
                const originalCreateOscillator = context.createOscillator;
                context.createOscillator = function () {
                    const oscillator = originalCreateOscillator.call(this);
                    const originalStart = oscillator.start;
                    oscillator.start = function (when) {
                        // 添加微小的时间偏移
                        return originalStart.call(this, when + Math.random() * 0.0001);
                    };
                    return oscillator;
                };

                return context;
            }

            window.AudioContext = PatchedAudioContext;
            if (window.webkitAudioContext) {
                window.webkitAudioContext = PatchedAudioContext;
            }
        }

        // 伪装字体检测
        const originalOffsetWidth = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetWidth');
        const originalOffsetHeight = Object.getOwnPropertyDescriptor(HTMLElement.prototype, 'offsetHeight');

        if (originalOffsetWidth) {
            Object.defineProperty(HTMLElement.prototype, 'offsetWidth', {
                get: function () {
                    const width = originalOffsetWidth.get.call(this);
                    // 为字体检测添加微小但一致的变化
                    if (this.style && this.style.fontFamily) {
                        const hash = this.style.fontFamily.split('').reduce((a, b) => {
                            a = ((a << 5) - a) + b.charCodeAt(0);
                            return a & a;
                        }, 0);
                        return width + (hash % 3 - 1) * 0.1;
                    }
                    return width;
                },
                configurable: true
            });
        }

        // 伪装媒体设备
        if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
            const originalEnumerateDevices = navigator.mediaDevices.enumerateDevices;
            navigator.mediaDevices.enumerateDevices = function () {
                return originalEnumerateDevices.call(this).then(devices => {
                    // 返回标准的设备列表
                    return [
                        { deviceId: 'default', groupId: 'group1', kind: 'audioinput', label: 'Default - Microphone' },
                        { deviceId: 'default', groupId: 'group2', kind: 'audiooutput', label: 'Default - Speaker' },
                        { deviceId: 'default', groupId: 'group3', kind: 'videoinput', label: 'Default - Camera' }
                    ];
                });
            };
        }

        // 伪装电池API
        if (navigator.getBattery) {
            navigator.getBattery = function () {
                return Promise.resolve({
                    charging: true,
                    chargingTime: 0,
                    dischargingTime: Infinity,
                    level: 1.0,
                    addEventListener: function () { },
                    removeEventListener: function () { }
                });
            };
        }

        // 伪装连接信息
        if (navigator.connection) {
            Object.defineProperty(navigator, 'connection', {
                get: () => ({
                    effectiveType: '4g',
                    rtt: 50,
                    downlink: 10,
                    saveData: false
                }),
                configurable: true
            });
        }

        // 添加真实的鼠标和键盘事件监听器
        ['mousedown', 'mouseup', 'mousemove', 'keydown', 'keyup', 'scroll', 'focus', 'blur'].forEach(eventType => {
            document.addEventListener(eventType, () => { }, true);
        });

        // 伪装性能API
        if (window.performance && window.performance.memory) {
            Object.defineProperty(window.performance, 'memory', {
                get: () => ({
                    usedJSHeapSize: 10000000 + Math.random() * 5000000,
                    totalJSHeapSize: 20000000 + Math.random() * 10000000,
                    jsHeapSizeLimit: 2172649472
                }),
                configurable: true
            });
        }
    });
}

/**
 * 模拟人类行为的延迟
 */
function humanDelay() {
    return randomBetween(1500, 4000); // 增加延迟时间，更像真人
}

/**
 * 生成长时间思考延迟（用于关键决策点）
 */
function longHumanDelay() {
    return randomBetween(3000, 8000); // 3-8秒的长时间思考
}

/**
 * 模拟人类打字行为
 */
async function humanType(page, selector, text) {
    const element = await page.$(selector);
    if (!element) return false;

    await element.click();
    await page.waitForTimeout(humanDelay());

    for (const char of text) {
        await element.type(char, { delay: randomBetween(50, 150) });
        if (Math.random() < 0.1) { // 10%的概率暂停
            await page.waitForTimeout(randomBetween(200, 500));
        }
    }
    return true;
}

/**
 * 模拟人类鼠标移动和点击
 */
async function humanClick(page, selector) {
    const element = await page.$(selector);
    if (!element) return false;

    const box = await element.boundingBox();
    if (!box) return false;

    // 随机点击位置
    const x = box.x + box.width * (0.3 + Math.random() * 0.4);
    const y = box.y + box.height * (0.3 + Math.random() * 0.4);

    // 模拟鼠标移动
    await page.mouse.move(x, y, { steps: randomBetween(5, 15) });
    await page.waitForTimeout(randomBetween(100, 300));

    // 点击
    await page.mouse.click(x, y, { delay: randomBetween(50, 150) });
    return true;
}

module.exports = {
    getEnhancedLaunchOptions,
    applyAntiFingerprinting,
    humanDelay,
    longHumanDelay,
    humanType,
    humanClick,
    randomBetween,
    randomString,
    getRandomUserAgent
};
