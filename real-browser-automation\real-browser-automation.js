const { connect } = require('puppeteer-real-browser');
const fs = require('fs');
const path = require('path');

const RealBrowserConfig = require('./config.js');
const RealBrowserLogger = require('./real-browser-logger.js');

// 复用现有的处理器
const TokenHandler = require('../token-api/token.js');
const CaptchaHandler = require('../headless-automation/captcha.js');
const ProxyHandler = require('../headless-automation/proxy.js');
const EmailHandler = require('../headless-automation/email.js');
const OneMailHandler = require('../headless-automation/onemail.js');
const AugmentAuth = require('../headless-automation/augment-auth.js');
const TokenStorage = require('../headless-automation/token-storage.js');

/**
 * Real Browser Automation
 * 使用 puppeteer-real-browser 进行真实浏览器自动化
 */
class RealBrowserAutomation {
    constructor() {
        this.config = new RealBrowserConfig();
        this.logger = new RealBrowserLogger();
        
        this.browser = null;
        this.page = null;
        this.tempEmail = null;
        
        // 复用现有的处理器
        this.tokenHandler = new TokenHandler();
        this.captchaHandler = new CaptchaHandler();
        this.proxyHandler = new ProxyHandler();
        this.emailHandler = new EmailHandler();
        this.oneMailHandler = new OneMailHandler();
        this.augmentAuth = new AugmentAuth();
        this.tokenStorage = new TokenStorage();
        
        this.logger.log('🤖 Real Browser Automation 初始化完成');
    }
    
    async initBrowser() {
        this.logger.log('🚀 启动真实浏览器...');
        this.config.printConfig();

        try {
            const browserOptions = {
                headless: false,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--start-maximized'
                ],
                connectOption: {
                    defaultViewport: null
                },
                customConfig: {},
                turnstile: true,
                disableXvfb: false,
                ignoreAllFlags: false
            };

            // 如果启用代理，添加代理配置
            if (this.config.real_browser_proxy) {
                const proxyConfig = this.config.getProxyConfig();
                if (proxyConfig) {
                    browserOptions.proxy = {
                        host: proxyConfig.host,
                        port: proxyConfig.port,
                        username: proxyConfig.username,
                        password: proxyConfig.password
                    };
                    this.logger.log(`🌐 使用代理: ${proxyConfig.host}:${proxyConfig.port}`);
                }
            }

            // 连接真实浏览器
            const { browser, page } = await connect(browserOptions);

            this.browser = browser;
            this.page = page;

            // 设置页面超时 - 检查方法是否存在
            if (typeof this.page.setDefaultTimeout === 'function') {
                this.page.setDefaultTimeout(this.config.page_timeout);
            }
            if (typeof this.page.setDefaultNavigationTimeout === 'function') {
                this.page.setDefaultNavigationTimeout(this.config.page_timeout);
            }

            // 设置用户代理
            await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

            this.logger.log('✅ 真实浏览器启动成功');

            // 记录浏览器启动步骤
            await this.logger.captureStep(this.page, 'browser_started', '真实浏览器启动成功');

            return true;

        } catch (error) {
            this.logger.error('❌ 真实浏览器启动失败', error);
            throw error;
        }
    }
    
    async navigateToPage(url) {
        this.logger.log(`🌐 导航到页面: ${url}`);

        try {
            await this.page.goto(url, {
                waitUntil: 'networkidle2',
                timeout: this.config.page_timeout
            });

            // 等待页面完全加载 - 使用 setTimeout 替代 waitForTimeout
            await new Promise(resolve => setTimeout(resolve, 2000));

            await this.logger.captureStep(this.page, 'page_loaded', `页面加载完成: ${url}`);

            this.logger.log('✅ 页面导航成功');
            return true;

        } catch (error) {
            this.logger.error('❌ 页面导航失败', error);
            await this.logger.captureError(this.page, 'navigation_failed', error.message);
            throw error;
        }
    }
    
    async handleCaptcha() {
        this.logger.log('🔍 检查是否存在验证码...');

        try {
            // 等待页面稳定
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 检查多种验证码类型 - 与 headless-automation 相同的检测逻辑
            const captchaSelectors = [
                'div[data-captcha-sitekey]',           // Auth0 v2 / Turnstile
                'iframe[src*="recaptcha"]',            // reCAPTCHA
                'iframe[src*="hcaptcha"]',             // hCaptcha
                '.cf-turnstile',                       // Cloudflare Turnstile
                '[id*="turnstile"]',                   // Turnstile ID
                '[class*="turnstile"]',                // Turnstile class
                '.g-recaptcha',                        // Google reCAPTCHA
                '[id*="recaptcha"]',                   // reCAPTCHA ID
                '[class*="captcha"]',                  // 通用验证码
                '[id*="captcha"]'                      // 验证码 ID
            ];

            let captchaFound = false;
            let captchaType = '';

            for (const selector of captchaSelectors) {
                const captchaElement = await this.page.$(selector);
                if (captchaElement) {
                    captchaFound = true;
                    captchaType = selector;
                    this.logger.log(`🤖 检测到验证码: ${selector}`);
                    break;
                }
            }

            if (captchaFound) {
                await this.logger.captureStep(this.page, 'captcha_detected', `检测到验证码: ${captchaType}`);

                // 这个初始验证码总是自动解决，与 headless-automation 一致
                this.logger.log('🚀 开始自动解决初始验证码...');

                // 启用高分数模式
                this.captchaHandler.enableHighScoreMode();

                // 获取验证码信息
                const currentUrl = this.page.url();
                const siteKeyElement = await this.page.$('div[data-captcha-sitekey]');
                let siteKey = null;

                if (siteKeyElement) {
                    siteKey = await this.page.evaluate(el => el.getAttribute('data-captcha-sitekey'), siteKeyElement);
                    this.logger.log(`🔑 获取到 siteKey: ${siteKey}`);
                }

                let success = false;

                // 根据验证码类型使用相应的处理方法
                if (captchaType === 'div[data-captcha-sitekey]' && siteKey) {
                    // Turnstile 验证码
                    this.logger.log('🎯 使用 YesCaptcha 处理 Turnstile 验证码...');
                    success = await this.captchaHandler.handleTurnstile(this.page, currentUrl, siteKey);
                } else {
                    // 其他类型验证码，尝试通用处理
                    this.logger.log('🎯 使用通用验证码处理...');
                    // 这里可以添加其他验证码类型的处理逻辑
                    success = false;
                }

                if (success) {
                    this.logger.log('✅ 初始验证码解决成功');
                    await this.logger.captureStep(this.page, 'captcha_solved', '初始验证码解决成功');

                    // 与 headless-automation 完全一致：等待5秒让页面处理token
                    await new Promise(resolve => setTimeout(resolve, 5000));

                    // 检查token注入后的状态 - 与 headless-automation 一致
                    const afterTokenStatus = await this.checkCaptchaStatus();
                    this.logger.log(`Token注入后状态: ${JSON.stringify(afterTokenStatus, null, 2)}`);

                    await this.logger.captureStep(this.page, 'after_token_injection', 'Token注入后状态检查');

                    return true;
                } else {
                    this.logger.warn('⚠️ 初始验证码解决失败');
                    await this.logger.captureStep(this.page, 'captcha_failed', '初始验证码解决失败');
                    throw new Error('初始验证码解决失败');
                }
            } else {
                this.logger.log('✅ 未检测到验证码');
                return true;
            }

        } catch (error) {
            this.logger.error('❌ 验证码处理失败', error);
            await this.logger.captureError(this.page, 'captcha_error', error.message);
            throw error;
        }
    }
    
    // 复用 headless-automation 的 findInputField 方法
    async findInputField(selectors) {
        for (const selector of selectors) {
            try {
                await this.page.waitForSelector(selector, { timeout: 5000 });
                const input = await this.page.$(selector);
                if (input) return input;
            } catch (e) {
                continue;
            }
        }
        return null;
    }

    // 复用 headless-automation 的 clickButtonContaining 方法
    async clickButtonContaining(keywords, timeout = 15000) {
        try {
            await this.page.waitForSelector('button, a, input[type="submit"], [role="button"]', { timeout });
            const elements = await this.page.$$('button, a, input[type="submit"], [role="button"]');

            for (const element of elements) {
                const text = await this.page.evaluate(el => {
                    return (el.textContent || el.value || el.getAttribute('aria-label') || '').trim();
                }, element);

                for (const keyword of keywords) {
                    if (text.toLowerCase().includes(keyword.toLowerCase())) {
                        await element.click();
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        return true;
                    }
                }
            }
            return false;
        } catch (error) {
            return false;
        }
    }

    // 确保 turnstile token 在主表单中（有些页面 token 被注入到局部 form，需要迁移）
    async ensureTurnstileTokenInForm() {
        try {
            await this.page.evaluate(() => {
                const primaryForm = document.querySelector('form[data-form-primary="true"], form[action*="/login"], form[action*="/identifier"]') || document.querySelector('form');
                if (!primaryForm) return;

                // 查找 token 输入
                const tokenInputs = Array.from(document.querySelectorAll('input[name="cf-turnstile-response"], input[name*="turnstile"], input[name*="captcha"], input[name*="token"]'));
                tokenInputs.forEach(input => {
                    if (input.form !== primaryForm) {
                        // 迁移 token 到主表单
                        const clone = input.cloneNode(true);
                        primaryForm.appendChild(clone);
                    }
                });
            });
        } catch (e) {
            this.logger.warn('ensureTurnstileTokenInForm 出错: ' + e.message);
        }
    }

    async programmaticSubmitPrimaryForm() {
        try {
            await this.page.evaluate(() => {
                const primaryForm = document.querySelector('form[data-form-primary="true"], form[action*="/login"], form[action*="/identifier"]') || document.querySelector('form');
                if (primaryForm) primaryForm.submit();
            });
        } catch (e) {
            this.logger.warn('programmaticSubmitPrimaryForm 出错: ' + e.message);
        }
    }

    // 同步 YesCaptcha 注入的 token 到 Auth0 ULP 期望的隐藏字段（name="captcha"）
    async syncCaptchaHiddenInput() {
        try {
            const status = await this.page.evaluate(() => {
                const tokenInput = document.querySelector('input[name="cf-turnstile-response"]');
                const captchaInput = document.querySelector('div[data-captcha-provider="auth0_v2"] input[name="captcha"], input[name="captcha"]');
                let copied = false;

                if (tokenInput && tokenInput.value) {
                    if (captchaInput) {
                        // 将 token 复制到 Auth0 的隐藏字段
                        captchaInput.value = tokenInput.value;
                        // 触发事件以通过前端验证器
                        captchaInput.dispatchEvent(new Event('input', { bubbles: true }));
                        captchaInput.dispatchEvent(new Event('change', { bubbles: true }));
                        // 去除错误/待处理样式（如果有）
                        const container = captchaInput.closest('.ulp-captcha-container') || document.querySelector('.ulp-captcha-container');
                        if (container) {
                            try { container.classList.remove('c7cf794f8'); } catch (e) {}
                        }
                        copied = true;
                    }
                }
                return {
                    copied,
                    tokenLen: tokenInput?.value?.length || 0,
                    hasCaptchaInput: !!captchaInput,
                    captchaLen: captchaInput?.value?.length || 0
                };
            });

            this.logger.log('🔄 同步captcha隐藏字段: ' + JSON.stringify(status));
            await this.logger.captureStep(this.page, 'captcha_hidden_synced', '同步captcha隐藏字段到Auth0');
            return status.copied;
        } catch (e) {
            this.logger.warn('syncCaptchaHiddenInput 出错: ' + e.message);
            return false;
        }
    }

    async enterEmail(email) {
        this.logger.log(`📧 输入邮箱: ${email}`);

        try {
            // 使用与 headless-automation 完全相同的选择器列表
            const emailInputSelectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[name="username"]',
                'input[id="username"]',
                'input[inputmode="email"]',
                'input[placeholder*="email"]',
                'input[placeholder*="Email"]',
                'input[id*="email"]',
                'input[class*="email"]'
            ];

            const emailInput = await this.findInputField(emailInputSelectors);
            if (!emailInput) {
                throw new Error('未找到邮箱输入框');
            }

            // 使用更稳健的输入方式：滚动、聚焦、清空、键盘/DOM 双通道
            await this.page.evaluate(el => { try { el.scrollIntoView({ behavior: 'instant', block: 'center' }); el.focus(); } catch(e){} }, emailInput);
            await emailInput.click({ clickCount: 1 });
            await new Promise(r => setTimeout(r, 400));

            // 尝试 Ctrl+A 删除现有内容
            try {
                await this.page.keyboard.down('Control');
                await this.page.keyboard.press('KeyA');
                await this.page.keyboard.up('Control');
                await this.page.keyboard.press('Backspace');
                await new Promise(r => setTimeout(r, 200));
            } catch {}

            // 首选键入方式（模拟真人输入）
            await emailInput.type(email, { delay: 80 });
            await new Promise(r => setTimeout(r, 300));

            // 补发 input/change/blur 事件，触发表单校验
            await this.page.evaluate(el => {
                el.dispatchEvent(new Event('input', { bubbles: true }));
                el.dispatchEvent(new Event('change', { bubbles: true }));
                el.blur();
            }, emailInput);
            await new Promise(r => setTimeout(r, 200));

            // 校验是否完整写入，否则回退到 DOM 直接设置
            const written = await this.page.evaluate(el => el.value || el.getAttribute('value') || '', emailInput);
            if (!written || written.trim() !== email) {
                await this.page.evaluate((el, val) => {
                    el.value = val;
                    el.dispatchEvent(new Event('input', { bubbles: true }));
                    el.dispatchEvent(new Event('change', { bubbles: true }));
                    el.blur();
                }, emailInput, email);
                await new Promise(r => setTimeout(r, 200));
            }

            // 二次校验确保一致
            const finalVal = await this.page.evaluate(el => el.value || el.getAttribute('value') || '', emailInput);
            if (finalVal.trim() !== email) {
                throw new Error(`邮箱写入不完整: got="${finalVal}" expected="${email}"`);
            }

            this.logger.log(`邮箱已填入: ${email}`);
            await this.logger.captureStep(this.page, 'email_entered', `邮箱输入完成: ${email}`);

            this.logger.log('✅ 邮箱输入成功');
            return true;

        } catch (error) {
            this.logger.error('❌ 邮箱输入失败', error);
            await this.logger.captureError(this.page, 'email_input_failed', error.message);
            throw error;
        }
    }

    async clickContinue() {
        this.logger.log('🔄 点击继续按钮...');

        try {
            const beforeUrl = this.page.url();

            // 优先确保 token 在主表单里，并同步到 Auth0 的 captcha 隐藏字段
            await this.ensureTurnstileTokenInForm();
            await this.syncCaptchaHiddenInput();

            // 在点击前确保按钮可用：移除 disabled/aria-disabled 并触发校验器
            await this.page.evaluate(() => {
                const btn = document.querySelector('button[type="submit"]._button-login-id, button[type="submit"][data-action-button-primary="true"], button._button-login-id');
                const form = document.querySelector('form[data-form-primary="true"]') || document.querySelector('form');
                if (btn) {
                    btn.removeAttribute('disabled');
                    btn.removeAttribute('aria-disabled');
                }
                if (form) {
                    // 触发表单验证器（Auth0 ULP 带有自定义校验）
                    const email = form.querySelector('input[name="username"], input[type="email"]');
                    if (email) {
                        email.dispatchEvent(new Event('input', { bubbles: true }));
                        email.dispatchEvent(new Event('change', { bubbles: true }));
                        email.blur();
                    }
                    const captcha = form.querySelector('input[name="captcha"], input[name="cf-turnstile-response"]');
                    if (captcha && captcha.value) {
                        captcha.dispatchEvent(new Event('input', { bubbles: true }));
                        captcha.dispatchEvent(new Event('change', { bubbles: true }));
                    }
                }
            });

            // 使用与 headless-automation 相同的按钮点击逻辑
            const continueClicked = await this.clickButtonContaining(['Continue', 'Next', '继续', 'Submit']);
            if (continueClicked) {
                this.logger.log('✅ Continue按钮点击成功');
            } else {
                this.logger.log('未找到Continue按钮，尝试按Enter键');
                await this.page.keyboard.press('Enter');
            }

            // 等待页面跳转或验证码输入框出现（两者其一）
            let navigated = false;
            try {
                await this.page.waitForFunction((u) => {
                    const urlChanged = window.location.href !== u;
                    const codeInput = document.querySelector('input[name="code"], input[name="verification_code"], input[name="verificationCode"], input[autocomplete="one-time-code"]');
                    return urlChanged || !!codeInput;
                }, { timeout: 15000 }, beforeUrl);
                navigated = true;
            } catch {}

            // 若仍未跳转，直接提交主表单作为兜底
            const afterTryUrl = this.page.url();
            if (!navigated && afterTryUrl === beforeUrl) {
                this.logger.warn('⚠️ URL未变化，先再次同步captcha字段后使用表单.submit() 兜底提交');
                await this.syncCaptchaHiddenInput();
                await this.programmaticSubmitPrimaryForm();

                // 再等待一次
                try {
                    await this.page.waitForFunction((u) => window.location.href !== u, { timeout: 8000 }, beforeUrl);
                    navigated = true;
                } catch {}
            }

            await this.logger.captureStep(this.page, 'continue_clicked', '继续按钮点击完成');

            // 处理邮箱页面可能出现的验证码
            this.logger.log('🔍 检查邮箱页面是否有验证码...');
            await this.handleCaptchaIfPresent();

            // 严格断言：如果仍未跳转且也未出现验证码输入框，则认为点击失败，抛错
            const stillOnSameUrl = this.page.url() === beforeUrl;
            const hasCodeInput = await this.page.evaluate(() => !!document.querySelector('input[name="code"], input[name="verification_code"], input[name="verificationCode"], input[autocomplete="one-time-code"]'));
            if (stillOnSameUrl && !hasCodeInput) {
                await this.logger.captureError(this.page, 'continue_not_navigated', '点击Continue后未跳转且未出现验证码输入框');
                throw new Error('Continue 点击后未跳转，仍停留在邮箱页面');
            }

            return true;

        } catch (error) {
            this.logger.error('❌ 继续按钮点击失败', error);
            await this.logger.captureError(this.page, 'continue_click_failed', error.message);
            throw error;
        }
    }

    // 添加与 headless-automation 相同的 handleCaptchaIfPresent 方法
    async handleCaptchaIfPresent() {
        this.logger.log('🔍 检查当前页面是否存在验证码...');

        try {
            // 等待页面稳定
            await new Promise(resolve => setTimeout(resolve, 2000));

            // 检测验证码信息
            const captchaInfo = await this.detectCaptcha();

            if (!captchaInfo.hasTurnstile && !captchaInfo.hasRecaptcha) {
                this.logger.log('✅ 当前页面未检测到验证码');
                return true;
            }

            this.logger.log('🤖 当前页面检测到验证码');
            await this.logger.captureStep(this.page, 'page_captcha_detected', '页面验证码检测');

            let success = false;

            if (captchaInfo.hasTurnstile && captchaInfo.siteKey) {
                this.logger.log('🎯 使用 YesCaptcha 处理页面 Turnstile 验证码...');
                success = await this.captchaHandler.handleTurnstile(this.page, captchaInfo.currentUrl, captchaInfo.siteKey);
            } else if (captchaInfo.hasRecaptcha) {
                // 检测是否为自动提交页面
                const isAutoSubmitPage = await this.page.evaluate(() => {
                    return document.body.innerHTML.includes('Auto-submit logic') ||
                           document.body.innerHTML.includes('onClick') ||
                           document.querySelector('#action-form');
                });

                if (isAutoSubmitPage && this.config.real_browser_recaptcha_solve) {
                    this.logger.log('🎯 检测到自动提交页面，使用 YesCaptcha 解决 reCAPTCHA...');
                    success = await this.captchaHandler.handleRecaptchaEnterprise(this.page);
                } else {
                    this.logger.log('⏸️ reCAPTCHA 自动解决已禁用或非自动提交页面');
                    success = true; // 跳过处理
                }
            }

            if (success) {
                this.logger.log('✅ 页面验证码处理成功');
                await this.logger.captureStep(this.page, 'page_captcha_solved', '页面验证码解决成功');

                // 等待一段时间让页面处理token
                await new Promise(resolve => setTimeout(resolve, 5000));

                return true;
            } else {
                this.logger.warn('⚠️ 页面验证码处理失败');
                await this.logger.captureStep(this.page, 'page_captcha_failed', '页面验证码处理失败');
                return false;
            }

        } catch (error) {
            this.logger.error('❌ 页面验证码处理出错', error);
            await this.logger.captureError(this.page, 'page_captcha_error', error.message);
            return false;
        }
    }

    // 添加验证码检测方法
    async detectCaptcha() {
        return await this.page.evaluate(() => {
            const result = {
                hasTurnstile: false,
                hasRecaptcha: false,
                siteKey: null,
                currentUrl: window.location.href
            };

            // 检测 Turnstile
            const turnstileIframes = document.querySelectorAll('iframe[src*="challenges.cloudflare.com"]');
            if (turnstileIframes.length > 0) {
                result.hasTurnstile = true;
                const src = turnstileIframes[0].src;
                const match = src.match(/0x4[A-Za-z0-9]{20,}/);
                if (match) result.siteKey = match[0];
            }

            const siteKeyElements = document.querySelectorAll('[data-sitekey]');
            if (siteKeyElements.length > 0) {
                const sitekey = siteKeyElements[0].getAttribute('data-sitekey');
                if (sitekey && sitekey.startsWith('0x4')) {
                    result.hasTurnstile = true;
                    result.siteKey = sitekey;
                }
            }

            // 检测 reCAPTCHA
            const recaptchaElements = document.querySelectorAll('iframe[src*="recaptcha"], .g-recaptcha, [data-sitekey*="6L"]');
            if (recaptchaElements.length > 0) {
                result.hasRecaptcha = true;
            }

            // 检测 reCAPTCHA Enterprise
            if (window.grecaptcha && window.grecaptcha.enterprise) {
                result.hasRecaptcha = true;
            }

            return result;
        });
    }

    // 添加与 headless-automation 完全一致的 checkCaptchaStatus 方法
    async checkCaptchaStatus() {
        return await this.page.evaluate(() => {
            const result = {
                isResolved: false,
                turnstileStatus: 'unknown',
                hiddenInputs: [],
                visibleElements: [],
                errors: []
            };

            try {
                // 检查Turnstile响应字段
                const turnstileInputs = document.querySelectorAll('input[name="cf-turnstile-response"]');
                turnstileInputs.forEach((input, index) => {
                    result.hiddenInputs.push({
                        type: 'turnstile-response',
                        index,
                        hasValue: input.value && input.value.length > 0,
                        valueLength: input.value ? input.value.length : 0
                    });

                    if (input.value && input.value.length > 0) {
                        result.isResolved = true;
                        result.turnstileStatus = 'resolved';
                    }
                });

                // 检查其他验证码相关的隐藏字段
                const otherInputs = document.querySelectorAll('input[name*="captcha"], input[name*="token"]');
                otherInputs.forEach((input, index) => {
                    if (input.name !== 'cf-turnstile-response') {
                        result.hiddenInputs.push({
                            type: 'other-captcha',
                            name: input.name,
                            index,
                            hasValue: input.value && input.value.length > 0,
                            valueLength: input.value ? input.value.length : 0
                        });
                    }
                });

                // 检查可见的验证码元素
                const visibleCaptchaElements = document.querySelectorAll('.cf-turnstile, .g-recaptcha, [data-sitekey]');
                visibleCaptchaElements.forEach((element, index) => {
                    result.visibleElements.push({
                        type: element.className.includes('cf-turnstile') ? 'turnstile' :
                              element.className.includes('g-recaptcha') ? 'recaptcha' : 'unknown',
                        index,
                        isVisible: element.offsetParent !== null,
                        hasDataSitekey: element.hasAttribute('data-sitekey')
                    });
                });

            } catch (error) {
                result.errors.push(error.message);
            }

            return result;
        });
    }

    async waitForVerificationPage() {
        this.logger.log('⏳ 等待验证页面加载...');

        try {
            // 更严格的验证码输入框选择器，避免误判 username
            const codeInputSelectors = [
                'input[name="code"]',
                'input[name="verification_code"]',
                'input[name="verificationCode"]',
                'input[autocomplete="one-time-code"]',
                'input[placeholder*="code" i]',
                'input[placeholder*="验证码" i]',
                'input[id*="code" i]',
                'input[class*="code" i]'
            ];

            const codeInput = await this.findInputField(codeInputSelectors);
            if (!codeInput) {
                throw new Error('未找到验证码输入框（已排除 username 的误判）');
            }

            await this.logger.captureStep(this.page, 'verification_page_loaded', '验证页面加载完成');

            this.logger.log('✅ 验证页面加载成功');
            return true;

        } catch (error) {
            this.logger.error('❌ 验证页面加载失败', error);
            await this.logger.captureError(this.page, 'verification_page_failed', error.message);
            throw error;
        }
    }

    async enterVerificationCode(code) {
        this.logger.log(`🔢 输入验证码: ${code}`);

        try {
            // 使用与 headless-automation 完全相同的验证码输入框选择器
            const codeInputSelectors = [
                'input[type="text"]',
                'input[name="code"]',
                'input[name="verification_code"]',
                'input[name="verificationCode"]',
                'input[placeholder*="code"]',
                'input[placeholder*="Code"]',
                'input[placeholder*="验证码"]',
                'input[id*="code"]',
                'input[class*="code"]'
            ];

            const codeInput = await this.findInputField(codeInputSelectors);
            if (!codeInput) {
                throw new Error('未找到验证码输入框');
            }

            // 使用与 headless-automation 相同的输入方式
            await codeInput.click();
            await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            await codeInput.type(code, { delay: 100 });

            await this.logger.captureStep(this.page, 'code_entered', `验证码输入完成: ${code}`);

            this.logger.log('✅ 验证码输入成功');
            return true;

        } catch (error) {
            this.logger.error('❌ 验证码输入失败', error);
            await this.logger.captureError(this.page, 'code_input_failed', error.message);
            throw error;
        }
    }

    async waitForAuthorizationCode() {
        this.logger.log('⏳ 等待授权码页面...');

        try {
            // 等待授权码出现或复制按钮出现
            await this.page.waitForFunction(() => {
                // 检查复制按钮
                const buttons = Array.from(document.querySelectorAll('button'));
                const copyButton = buttons.find(btn =>
                    btn.textContent?.toLowerCase().includes('copy') ||
                    btn.textContent?.toLowerCase().includes('复制')
                );

                // 检查授权码元素
                const codeElement = document.querySelector('[data-testid="authorization-code"], .authorization-code, #authorization-code');

                // 检查页面文本中是否包含授权码
                const hasAuthCode = document.body.innerText.includes('authorization_code=') ||
                                   document.body.innerText.includes('code=') ||
                                   document.body.innerText.includes('Authorization Code') ||
                                   document.body.innerText.includes('授权码');

                return copyButton || codeElement || hasAuthCode;
            }, { timeout: 30000 });

            await this.logger.captureStep(this.page, 'authorization_page_loaded', '授权码页面加载完成');

            this.logger.log('✅ 授权码页面加载成功');
            return true;

        } catch (error) {
            this.logger.error('❌ 授权码页面加载失败', error);
            await this.logger.captureError(this.page, 'authorization_page_failed', error.message);
            throw error;
        }
    }

    async extractAuthorizationCode() {
        this.logger.log('📋 提取授权码...');

        try {
            // 等待按钮出现，保证页面资源完全加载
            try {
                await this.page.waitForSelector('#copyButton, button:has-text("Copy")', { timeout: 10000 });
            } catch {}

            // 策略0: 劫持 clipboard.writeText 并点击按钮，直接获取 JSON 字符串
            try {
                const hijacked = await this.page.evaluate(() => {
                    try {
                        window.__authCopied = null;
                        const btn = document.getElementById('copyButton') || Array.from(document.querySelectorAll('button')).find(b => (b.textContent||'').toLowerCase().includes('copy'));
                        if (!btn) return { installed: false };

                        // 尝试覆盖 clipboard.writeText
                        let installed = false;
                        try {
                            const original = navigator.clipboard.writeText;
                            Object.defineProperty(navigator.clipboard, 'writeText', { configurable: true, writable: true, value: (text) => { window.__authCopied = text; return Promise.resolve(); } });
                            installed = true;
                        } catch (e) {
                            // 一些环境不可覆盖，退而求其次：在捕获阶段拦截点击并自行组装
                            btn.addEventListener('click', function () {
                                try {
                                    const scripts = Array.from(document.querySelectorAll('script'));
                                    for (const s of scripts) {
                                        const c = s.textContent || s.innerHTML || '';
                                        const m = c.match(/let\s+data\s*=\s*(\{[\s\S]*?\})/);
                                        if (m) {
                                            try {
                                                // 直接 eval 对象字面量（仅作用于匹配到的对象文本）
                                                const obj = (0, eval)('(' + m[1] + ')');
                                                if (obj && obj.code && obj.state && obj.tenant_url) {
                                                    window.__authCopied = JSON.stringify(obj);
                                                    break;
                                                }
                                            } catch {}
                                        }
                                    }
                                } catch {}
                            }, { capture: true, once: true });
                        }

                        btn.click();
                        return { installed };
                    } catch (e) {
                        return { installed: false, error: e.message };
                    }
                });

                // 给页面时间执行监听
                await new Promise(r => setTimeout(r, 800));

                const captured = await this.page.evaluate(() => window.__authCopied);
                if (captured && captured.trim()) {
                    await this.logger.captureStep(this.page, 'authorization_copied_intercepted', '通过拦截clipboard获取授权码');
                    this.logger.log(`📋 通过拦截clipboard获取授权码: ${captured.substring(0, 120)}...`);
                    return captured.trim();
                }
            } catch {}

            // 策略1: 尝试点击复制按钮（原逻辑）+ 本地读取剪贴板（可能失败）
            const copyButtonFound = await this.page.evaluate(() => {
                const buttons = Array.from(document.querySelectorAll('button'));
                const copyButton = buttons.find(btn =>
                    btn.textContent?.toLowerCase().includes('copy') ||
                    btn.textContent?.toLowerCase().includes('复制')
                );
                if (copyButton) {
                    copyButton.click();
                    return true;
                }
                return false;
            });

            if (copyButtonFound) {
                this.logger.log('✅ 复制按钮点击成功');
                await new Promise(resolve => setTimeout(resolve, 800));
                try {
                    const clipboardy = require('clipboardy');
                    const clipboardContent = clipboardy.readSync();
                    if (clipboardContent && clipboardContent.trim()) {
                        await this.logger.captureStep(this.page, 'authorization_copied', '授权码复制完成');
                        this.logger.log(`📋 从剪贴板获取授权码: ${clipboardContent.substring(0, 120)}...`);
                        return clipboardContent.trim();
                    }
                } catch (clipboardError) {
                    this.logger.warn('剪贴板读取失败，尝试其他方法');
                }
            }

            // 策略2: 从页面JavaScript中提取完整的授权码数据
            const authDataFromScript = await this.page.evaluate(() => {
                try {
                    const scripts = Array.from(document.querySelectorAll('script'));
                    for (let i = 0; i < scripts.length; i++) {
                        const content = scripts[i].textContent || scripts[i].innerHTML || '';
                        // 宽松匹配对象字面量
                        const dataMatch = content.match(/let\s+data\s*=\s*(\{[\s\S]*?\})/);
                        if (dataMatch) {
                            const objTxt = dataMatch[1];
                            try {
                                const obj = (0, eval)('(' + objTxt + ')');
                                if (obj && obj.code && obj.state && obj.tenant_url) {
                                    return JSON.stringify(obj);
                                }
                            } catch {}
                            // 回退：单字段匹配
                            const codeMatch = objTxt.match(/code:\s*["']([^"']+)["']/);
                            const stateMatch = objTxt.match(/state:\s*["']([^"']+)["']/);
                            const tenantMatch = objTxt.match(/tenant_url:\s*["']([^"']+)["']/);
                            if (codeMatch && stateMatch && tenantMatch) {
                                return JSON.stringify({ code: codeMatch[1], state: stateMatch[1], tenant_url: tenantMatch[1] });
                            }
                        }
                        // 跨脚本的单字段匹配
                        const codeMatch = content.match(/["']?code["']?\s*:\s*["']([^"']+)["']/);
                        const stateMatch = content.match(/["']?state["']?\s*:\s*["']([^"']+)["']/);
                        const tenantMatch = content.match(/["']?tenant_url["']?\s*:\s*["']([^"']+)["']/);
                        if (codeMatch && stateMatch && tenantMatch) {
                            return JSON.stringify({ code: codeMatch[1], state: stateMatch[1], tenant_url: tenantMatch[1] });
                        }
                    }
                    return null;
                } catch { return null; }
            });

            if (authDataFromScript) {
                await this.logger.captureStep(this.page, 'authorization_extracted_script', '从JavaScript提取授权码完成');
                this.logger.log(`🎯 从页面JavaScript提取授权码数据: ${authDataFromScript.substring(0, 120)}...`);
                return authDataFromScript;
            }

            // 策略3: 从页面URL提取（很少发生）
            const currentUrl = this.page.url();
            let authCodeMatch = currentUrl.match(/[?&]code=([^&]+)/) || currentUrl.match(/[?&]authorization_code=([^&]+)/);
            if (authCodeMatch) {
                const authCode = decodeURIComponent(authCodeMatch[1]);
                await this.logger.captureStep(this.page, 'authorization_extracted_url', '从URL提取授权码完成');
                this.logger.log(`🔗 从URL提取授权码: ${authCode}`);
                return JSON.stringify({ code: authCode, state: this.oauthState?.state || '', tenant_url: this.oauthState?.tenant_url || '' });
            }

            // 策略4: 从页面内容正则提取（全页面扫描，极端兜底）
            const pageContent = await this.page.content();

            // 4.1 尝试匹配 let data = {...}
            authCodeMatch = pageContent.match(/let\s+data\s*=\s*(\{[\s\S]*?\})/);
            if (authCodeMatch) {
                try {
                    const obj = (0, eval)('(' + authCodeMatch[1] + ')');
                    if (obj && obj.code && obj.state && obj.tenant_url) {
                        await this.logger.captureStep(this.page, 'authorization_extracted_content', '从页面内容提取授权码完成');
                        return JSON.stringify(obj);
                    }
                } catch {}
            }

            // 4.2 直接在整页内容里提取三个字段并组装
            try {
                const codeM = pageContent.match(/["']?code["']?\s*:\s*["']([^"']+)["']/);
                const stateM = pageContent.match(/["']?state["']?\s*:\s*["']([^"']+)["']/);
                const tenantM = pageContent.match(/["']?tenant_url["']?\s*:\s*["']([^"']+)["']/);
                if (codeM && stateM && tenantM) {
                    const payload = { code: codeM[1], state: stateM[1], tenant_url: tenantM[1] };
                    await this.logger.captureStep(this.page, 'authorization_extracted_content_fields', '从页面内容字段提取授权码完成');
                    this.logger.log(`📄 从页面字段提取授权码数据: ${JSON.stringify(payload)}`);
                    return JSON.stringify(payload);
                }
            } catch {}

            throw new Error('未能提取到授权码');

        } catch (error) {
            this.logger.error('❌ 授权码提取失败', error);
            await this.logger.captureError(this.page, 'authorization_extract_failed', error.message);
            throw error;
        }
    }

    async cleanup() {
        this.logger.log('🧹 清理资源...');

        try {
            if (this.page) {
                await this.page.close();
                this.logger.log('✅ 页面已关闭');
            }

            if (this.browser) {
                await this.browser.close();
                this.logger.log('✅ 浏览器已关闭');
            }

        } catch (error) {
            this.logger.error('❌ 资源清理失败', error);
        }
    }
}

module.exports = RealBrowserAutomation;
