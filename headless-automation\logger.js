const fs = require('fs');
const path = require('path');

class Logger {
    constructor() {
        this.debugDir = path.join(__dirname, 'debug');
        this.logFile = path.join(this.debugDir, `network-logs-${new Date().toISOString().split('T')[0]}.log`);
        this.ensureDebugDir();
    }

    ensureDebugDir() {
        if (!fs.existsSync(this.debugDir)) {
            fs.mkdirSync(this.debugDir, { recursive: true });
        }
    }

    /**
     * 记录详细日志到文件，控制台显示精简版本
     * @param {string} type - 日志类型 (request, response, redirect, etc.)
     * @param {string} message - 完整消息
     * @param {string} shortMessage - 精简消息（可选）
     * @param {object} data - 额外数据（可选）
     */
    logNetwork(type, message, shortMessage = null, data = null) {
        const timestamp = new Date().toLocaleTimeString();
        
        // 完整日志写入文件
        const fullLogEntry = {
            timestamp: new Date().toISOString(),
            type,
            message,
            data
        };
        
        let logLine = `[${timestamp}] [${type.toUpperCase()}] ${message}\n`;
        if (data) {
            logLine += `Data: ${JSON.stringify(data, null, 2)}\n`;
        }
        logLine += '---\n';
        
        try {
            fs.appendFileSync(this.logFile, logLine);
        } catch (error) {
            console.error('写入日志文件失败:', error.message);
        }

        // 控制台显示精简版本
        const displayMessage = shortMessage || this.createShortMessage(type, message);
        console.log(`[${timestamp}] ${displayMessage}`);
    }

    /**
     * 创建精简消息
     * @param {string} type 
     * @param {string} fullMessage 
     * @returns {string}
     */
    createShortMessage(type, fullMessage) {
        switch (type) {
            case 'request':
                if (fullMessage.includes('📤 授权相关请求:')) {
                    const method = fullMessage.match(/GET|POST|PUT|DELETE/)?.[0] || '';
                    const domain = this.extractDomain(fullMessage);
                    return `📤 ${method} ${domain}`;
                }
                break;
            
            case 'response':
                if (fullMessage.includes('📥 授权相关响应:')) {
                    const status = fullMessage.match(/\d{3}/)?.[0] || '';
                    const domain = this.extractDomain(fullMessage);
                    return `📥 ${status} ${domain}`;
                }
                break;
            
            case 'redirect':
                if (fullMessage.includes('🔄 重定向到:')) {
                    const domain = this.extractDomain(fullMessage);
                    return `🔄 重定向到 ${domain}`;
                }
                break;
            
            case 'auth_success':
                return '🎉 检测到授权响应';
            
            default:
                // 如果消息太长，截断显示
                if (fullMessage.length > 100) {
                    return fullMessage.substring(0, 97) + '...';
                }
                return fullMessage;
        }
        
        return fullMessage;
    }

    /**
     * 从URL中提取域名
     * @param {string} message 
     * @returns {string}
     */
    extractDomain(message) {
        try {
            const urlMatch = message.match(/https?:\/\/([^\/\s?]+)/);
            if (urlMatch) {
                const domain = urlMatch[1];
                // 简化常见域名
                if (domain.includes('augmentcode.com')) return 'augmentcode.com';
                if (domain.includes('google.com')) return 'google.com';
                if (domain.includes('facebook.com')) return 'facebook.com';
                if (domain.includes('googleadservices.com')) return 'googleads';
                return domain.split('.').slice(-2).join('.');
            }
        } catch (error) {
            // 忽略解析错误
        }
        return 'unknown';
    }

    /**
     * 记录普通日志（不涉及网络请求）
     * @param {string} message 
     */
    log(message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] ${message}`);
    }

    /**
     * 保存调试信息到JSON文件
     * @param {string} filename 
     * @param {object} data 
     */
    saveDebugInfo(filename, data) {
        try {
            const filepath = path.join(this.debugDir, filename);
            const jsonData = JSON.stringify(data, null, 2);
            fs.writeFileSync(filepath, jsonData, 'utf8');
            this.log(`💾 调试信息已保存: ${filename}`);
        } catch (error) {
            this.log(`保存调试信息失败: ${error.message}`);
        }
    }

    /**
     * 清理旧的日志文件（保留最近7天）
     */
    cleanOldLogs() {
        try {
            const files = fs.readdirSync(this.debugDir);
            const logFiles = files.filter(file => file.startsWith('network-logs-') && file.endsWith('.log'));
            
            const sevenDaysAgo = new Date();
            sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
            
            logFiles.forEach(file => {
                const filePath = path.join(this.debugDir, file);
                const stats = fs.statSync(filePath);
                
                if (stats.mtime < sevenDaysAgo) {
                    fs.unlinkSync(filePath);
                    console.log(`🗑️ 已删除旧日志文件: ${file}`);
                }
            });
        } catch (error) {
            console.error('清理旧日志失败:', error.message);
        }
    }
}

module.exports = Logger;
