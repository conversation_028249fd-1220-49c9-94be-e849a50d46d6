const path = require('path');
const RealBrowserAutomation = require('./real-browser-automation.js');
const AugmentAuth = require('../headless-automation/augment-auth.js');
const TokenStorage = require('../headless-automation/token-storage.js');
const OneMailHandler = require('../headless-automation/onemail.js');
const SimpleLogger = require('./simple-logger.js');

// 加载根目录的 .env 文件
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

/**
 * Real Browser Email Verification Runner
 * 使用真实浏览器执行邮箱验证流程
 */
async function runRealBrowserVerification() {
    const automation = new RealBrowserAutomation();
    const augmentAuth = new AugmentAuth();
    const tokenStorage = new TokenStorage();
    const oneMailHandler = new OneMailHandler();
    const simpleLogger = new SimpleLogger();

    const startTime = Date.now();
    let tempEmail = null;

    try {
        automation.logger.logFlowStart();

        console.log('🚀 开始 Real Browser 邮箱验证流程');
        console.log('');
        
        // 步骤1: 生成授权URL
        console.log('🔐 步骤1: 生成 Augment 授权 URL');
        const authUrl = augmentAuth.generateAuthUrl();
        console.log(`✅ 授权 URL 已生成: ${authUrl}`);
        console.log('');
        
        // 步骤2: 生成临时邮箱
        console.log('📧 步骤2: 生成临时邮箱');
        tempEmail = await oneMailHandler.generateEmail();
        if (!tempEmail) {
            throw new Error('临时邮箱生成失败');
        }
        automation.tempEmail = tempEmail;
        console.log(`✅ 临时邮箱生成成功: ${tempEmail}`);
        console.log('');

        // 记录开始日志
        simpleLogger.logStart(tempEmail);

        // 步骤3: 启动真实浏览器
        console.log('🌐 步骤3: 启动真实浏览器');
        await automation.initBrowser();
        console.log('');
        
        // 步骤4: 导航到授权页面
        console.log('🔗 步骤4: 导航到授权页面');
        await automation.navigateToPage(authUrl);
        console.log('');

        // 步骤5: 输入邮箱（先输入完整邮箱再处理验证码）
        console.log('📧 步骤5: 输入邮箱');
        await automation.enterEmail(tempEmail);
        console.log('');

        // 步骤6: 处理Turnstile验证码（邮箱输入后再解决）
        console.log('🤖 步骤6: 处理验证码');
        await automation.handleCaptcha();
        console.log('');

        // 步骤7: 点击Continue（与 headless-automation 完全一致）
        console.log('🔄 步骤7: 点击Continue');
        await automation.clickContinue();
        console.log('');
        
        // 步骤8: 等待验证页面
        console.log('⏳ 步骤8: 等待验证页面');
        await automation.waitForVerificationPage();
        console.log('');
        
        // 步骤8: 获取验证码
        console.log('📨 步骤8: 获取邮箱验证码');
        console.log('⏰ 开始检查邮箱验证码（最多等待2分钟）...');

        const verificationCode = await oneMailHandler.getVerificationCode(tempEmail, 2);
        
        if (!verificationCode) {
            throw new Error('未能获取到验证码');
        }
        
        console.log(`✅ 验证码获取成功: ${verificationCode}`);
        console.log('');
        
        // 步骤9: 输入验证码
        console.log('🔢 步骤9: 输入验证码');
        await automation.enterVerificationCode(verificationCode);
        console.log('');

        // 步骤10: 点击最终继续
        console.log('🔄 步骤10: 点击最终继续');
        await automation.clickContinue();
        console.log('');

        // 步骤11: 等待授权码页面
        console.log('⏳ 步骤11: 等待授权码页面');
        await automation.waitForAuthorizationCode();
        console.log('');

        // 步骤12: 提取授权码
        console.log('📋 步骤12: 提取授权码');
        const authorizationCode = await automation.extractAuthorizationCode();
        
        if (!authorizationCode) {
            throw new Error('未能提取到授权码');
        }
        
        console.log(`✅ 授权码提取成功: ${authorizationCode}`);
        console.log('');
        
        // 步骤13: 完成OAuth流程
        console.log('🔐 步骤13: 完成 OAuth 流程');
        console.log('🚀 开始调用真实的 Augment API...');
        
        const tokenResponse = await augmentAuth.completeOAuthFlow(authorizationCode);
        
        console.log('✅ 真实 API 调用成功！获取到真实访问令牌！');
        console.log(`🔑 真实访问令牌: ${tokenResponse.access_token?.substring(0, 30)}...`);
        console.log(`🏢 租户 URL: ${tokenResponse.tenant_url}`);
        console.log('');
        
        // 步骤14: 保存令牌
        console.log('💾 步骤14: 保存令牌');
        const tokenId = tokenStorage.addToken(tokenResponse, {
            description: 'Real Browser token from Augment API via email verification',
            user_agent: 'real-browser-email-verification',
            session_id: `real_browser_session_${Date.now()}`,
            email: tempEmail
        });
        
        console.log(`✅ 真实令牌已保存到 tokens.json，ID: ${tokenId}`);
        console.log('');
        
        // 成功完成
        automation.logger.logFlowEnd(true);

        // 记录成功日志
        if (tempEmail) {
            simpleLogger.logSuccess(tempEmail);
        }

        const duration = Date.now() - startTime;
        console.log('🎉 Real Browser 邮箱验证流程成功完成！');
        console.log(`⏱️ 总耗时: ${(duration / 1000).toFixed(2)}秒`);
        console.log(`📊 总步骤数: ${automation.logger.stepCounter}`);
        console.log('');
        console.log('📁 查看详细记录:');
        console.log(`   📸 截图目录: ${automation.logger.imageDir}`);
        console.log(`   📄 HTML目录: ${automation.logger.htmlDir}`);
        console.log(`   📋 日志文件: ${automation.logger.logFile}`);
        console.log(`   💾 令牌文件: tokens.json`);

    } catch (error) {
        automation.logger.logFlowEnd(false);

        // 记录失败日志
        if (tempEmail) {
            simpleLogger.logFailure(tempEmail, error.message);
        }
        
        console.error('');
        console.error('💥 Real Browser 邮箱验证流程失败:', error.message);
        console.error('📁 请查看截图和HTML文件进行调试');
        console.error('📁 请查看日志文件获取详细错误信息');
        
        const stats = automation.logger.getStats();
        console.error(`📊 失败前执行步骤数: ${stats.stepCount}`);
        console.error(`⏱️ 失败前耗时: ${stats.durationStr}`);
        console.error(`📸 截图目录: ${stats.imageDir}`);
        console.error(`📄 HTML目录: ${stats.htmlDir}`);
        console.error(`📋 日志文件: ${stats.logFile}`);
        
        process.exit(1);
        
    } finally {
        // 清理资源
        await automation.cleanup();
    }
}

if (require.main === module) {
    runRealBrowserVerification();
}

module.exports = runRealBrowserVerification;
