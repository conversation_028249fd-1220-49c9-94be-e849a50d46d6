/**
 * Ubuntu环境专用抗指纹检测配置
 * 解决Ubuntu环境下被识别为机器人的关键问题
 */

const os = require('os');

/**
 * 检测是否为Ubuntu/Linux环境
 */
function isUbuntuEnvironment() {
    return os.platform() === 'linux';
}

/**
 * 获取Ubuntu环境下的增强启动参数
 */
function getUbuntuEnhancedLaunchOptions() {
    const baseOptions = {
        headless: true,
        args: [
            // 基础安全参数
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',

            // Ubuntu特定的抗检测参数
            '--disable-blink-features=AutomationControlled',
            '--disable-features=VizDisplayCompositor',
            '--disable-extensions-http-throttling',
            '--disable-ipc-flooding-protection',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-background-timer-throttling',
            '--disable-features=TranslateUI',
            '--disable-default-apps',
            '--disable-sync',
            '--disable-translate',
            '--hide-scrollbars',
            '--mute-audio',
            '--no-first-run',
            '--no-default-browser-check',
            '--no-pings',
            '--password-store=basic',
            '--use-mock-keychain',

            // 字体相关参数 - 关键！
            '--font-render-hinting=none',
            '--disable-font-subpixel-positioning',
            '--disable-lcd-text',
            '--force-color-profile=srgb',

            // WebGL和图形相关 - 避免暴露软件渲染
            '--disable-webgl',
            '--disable-webgl2',
            '--disable-3d-apis',
            '--disable-accelerated-2d-canvas',
            '--disable-accelerated-jpeg-decoding',
            '--disable-accelerated-mjpeg-decode',
            '--disable-accelerated-video-decode',
            '--disable-gpu-compositing',
            '--disable-gpu-rasterization',
            '--disable-gpu-sandbox',

            // 内存和性能优化
            '--memory-pressure-off',
            '--max_old_space_size=4096',
            '--disable-background-networking',
            '--disable-component-extensions-with-background-pages',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',

            // 随机窗口大小
            `--window-size=${1366 + Math.floor(Math.random() * 554)},${768 + Math.floor(Math.random() * 312)}`,

            // 语言设置
            '--lang=en-US,en',
            '--accept-lang=en-US,en;q=0.9'
        ],
        defaultViewport: {
            width: 1366 + Math.floor(Math.random() * 554),
            height: 768 + Math.floor(Math.random() * 312),
            deviceScaleFactor: 1,
            isMobile: false,
            hasTouch: false,
            isLandscape: true
        },
        timeout: 120000,
        ignoreDefaultArgs: ['--enable-automation', '--enable-blink-features=IdleDetection'],
        ignoreHTTPSErrors: true
    };

    return baseOptions;
}

/**
 * Ubuntu环境下的高级抗指纹脚本
 */
async function applyUbuntuAntiFingerprinting(page) {
    // 设置Windows风格的User-Agent
    const windowsUserAgents = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
    ];
    await page.setUserAgent(windowsUserAgents[Math.floor(Math.random() * windowsUserAgents.length)]);

    // 设置Windows风格的HTTP头
    await page.setExtraHTTPHeaders({
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'en-US,en;q=0.9',
        'Cache-Control': 'max-age=0',
        'Sec-Ch-Ua': '"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"',
        'Sec-Ch-Ua-Mobile': '?0',
        'Sec-Ch-Ua-Platform': '"Windows"',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Upgrade-Insecure-Requests': '1'
    });

    // 在每个新文档加载时执行Ubuntu特定的抗指纹脚本
    await page.evaluateOnNewDocument(() => {
        // 1. 隐藏webdriver属性
        Object.defineProperty(navigator, 'webdriver', {
            get: () => undefined,
            configurable: true
        });

        // 2. 伪装平台信息 - 关键！
        Object.defineProperty(navigator, 'platform', {
            get: () => 'Win32',
            configurable: true
        });

        Object.defineProperty(navigator, 'oscpu', {
            get: () => 'Windows NT 10.0; Win64; x64',
            configurable: true
        });

        // 3. 伪装字体列表 - 最关键的部分！
        const originalFonts = document.fonts;
        Object.defineProperty(document, 'fonts', {
            get: () => {
                const mockFonts = {
                    check: () => true,
                    load: () => Promise.resolve([]),
                    ready: Promise.resolve(),
                    status: 'loaded',
                    values: () => [],
                    entries: () => [],
                    keys: () => [],
                    size: 47,
                    forEach: () => {},
                    has: () => true,
                    add: () => {},
                    clear: () => {},
                    delete: () => true
                };
                return mockFonts;
            },
            configurable: true
        });

        // 4. 伪装字体检测 - 模拟Windows标准字体
        const windowsFonts = [
            'Arial', 'Times New Roman', 'Courier New', 'Verdana', 'Georgia', 'Palatino',
            'Garamond', 'Bookman', 'Comic Sans MS', 'Trebuchet MS', 'Arial Black',
            'Impact', 'Calibri', 'Cambria', 'Segoe UI', 'Tahoma', 'Microsoft Sans Serif',
            'Consolas', 'Lucida Console', 'Lucida Sans Unicode'
        ];

        // 重写Canvas文本渲染以模拟Windows字体
        const originalFillText = CanvasRenderingContext2D.prototype.fillText;
        CanvasRenderingContext2D.prototype.fillText = function(text, x, y, maxWidth) {
            // 添加微小的随机偏移来模拟Windows字体渲染
            const offset = (Math.random() - 0.5) * 0.1;
            return originalFillText.call(this, text, x + offset, y + offset, maxWidth);
        };

        // 5. 增强的WebGL指纹伪装
        const getParameter = WebGLRenderingContext.prototype.getParameter;
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            // 伪装成常见的Windows显卡
            if (parameter === 37445) { // UNMASKED_VENDOR_WEBGL
                return 'Google Inc. (NVIDIA)';
            }
            if (parameter === 37446) { // UNMASKED_RENDERER_WEBGL
                return 'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11-27.21.14.5671)';
            }
            if (parameter === 35724) { // SHADING_LANGUAGE_VERSION
                return 'WebGL GLSL ES 1.0 (OpenGL ES GLSL ES 1.0 Chromium)';
            }
            if (parameter === 7936) { // VERSION
                return 'WebGL 1.0 (OpenGL ES 2.0 Chromium)';
            }
            if (parameter === 7937) { // VENDOR
                return 'WebKit';
            }
            if (parameter === 7938) { // RENDERER
                return 'WebKit WebGL';
            }
            return getParameter.call(this, parameter);
        };

        // 6. 增强的Canvas指纹伪装
        const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
        HTMLCanvasElement.prototype.toDataURL = function() {
            const context = this.getContext('2d');
            if (context) {
                // 添加Windows特有的渲染噪声
                const imageData = context.getImageData(0, 0, this.width, this.height);
                const data = imageData.data;
                for (let i = 0; i < data.length; i += 4) {
                    // 添加微小的随机噪声来模拟Windows渲染
                    data[i] = Math.min(255, data[i] + (Math.random() - 0.5) * 2);
                    data[i + 1] = Math.min(255, data[i + 1] + (Math.random() - 0.5) * 2);
                    data[i + 2] = Math.min(255, data[i + 2] + (Math.random() - 0.5) * 2);
                }
                context.putImageData(imageData, 0, 0);
            }
            return originalToDataURL.apply(this, arguments);
        };

        // 7. 伪装硬件信息
        Object.defineProperty(navigator, 'hardwareConcurrency', {
            get: () => 8, // 模拟常见的8核CPU
            configurable: true
        });

        Object.defineProperty(navigator, 'deviceMemory', {
            get: () => 8, // 模拟8GB内存
            configurable: true
        });

        // 8. 伪装语言和地区设置
        Object.defineProperty(navigator, 'languages', {
            get: () => ['en-US', 'en'],
            configurable: true
        });

        Object.defineProperty(navigator, 'language', {
            get: () => 'en-US',
            configurable: true
        });

        // 9. 伪装插件信息
        Object.defineProperty(navigator, 'plugins', {
            get: () => [
                {
                    0: { type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format" },
                    description: "Portable Document Format",
                    filename: "internal-pdf-viewer",
                    length: 1,
                    name: "Chrome PDF Plugin"
                },
                {
                    0: { type: "application/pdf", suffixes: "pdf", description: "" },
                    description: "",
                    filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                    length: 1,
                    name: "Chrome PDF Viewer"
                }
            ],
            configurable: true
        });

        // 10. 伪装屏幕信息
        Object.defineProperty(screen, 'width', {
            get: () => 1920,
            configurable: true
        });
        Object.defineProperty(screen, 'height', {
            get: () => 1080,
            configurable: true
        });
        Object.defineProperty(screen, 'availWidth', {
            get: () => 1920,
            configurable: true
        });
        Object.defineProperty(screen, 'availHeight', {
            get: () => 1040,
            configurable: true
        });
        Object.defineProperty(screen, 'colorDepth', {
            get: () => 24,
            configurable: true
        });
        Object.defineProperty(screen, 'pixelDepth', {
            get: () => 24,
            configurable: true
        });

        // 11. 移除自动化痕迹
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
        delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;

        // 12. 伪装chrome对象
        window.chrome = {
            runtime: {
                onConnect: undefined,
                onMessage: undefined
            },
            loadTimes: function () {
                return {
                    commitLoadTime: Date.now() / 1000 - Math.random() * 100,
                    connectionInfo: 'http/1.1',
                    finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 10,
                    finishLoadTime: Date.now() / 1000 - Math.random() * 10,
                    firstPaintAfterLoadTime: 0,
                    firstPaintTime: Date.now() / 1000 - Math.random() * 10,
                    navigationType: 'Other',
                    npnNegotiatedProtocol: 'unknown',
                    requestTime: Date.now() / 1000 - Math.random() * 100,
                    startLoadTime: Date.now() / 1000 - Math.random() * 100,
                    wasAlternateProtocolAvailable: false,
                    wasFetchedViaSpdy: false,
                    wasNpnNegotiated: false
                };
            }
        };

        // 13. 伪装权限API
        const originalQuery = window.navigator.permissions.query;
        window.navigator.permissions.query = (parameters) => (
            parameters.name === 'notifications' ?
                Promise.resolve({ state: 'default' }) :
                originalQuery(parameters)
        );

        // 14. 添加真实的事件监听器
        ['mousedown', 'mouseup', 'mousemove', 'keydown', 'keyup', 'scroll', 'touchstart'].forEach(eventType => {
            document.addEventListener(eventType, () => { }, true);
        });
    });
}

module.exports = {
    isUbuntuEnvironment,
    getUbuntuEnhancedLaunchOptions,
    applyUbuntuAntiFingerprinting
};
