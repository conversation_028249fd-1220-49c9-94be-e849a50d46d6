"""
Verisoul 逆向工程模块 - DrissionPage 版本
专门用于分析和对抗 Verisoul 反欺诈检测系统
"""

import time
import json
import logging
from typing import Dict, List, Any, Optional

class VerisoulReverseEngineering:
    """Verisoul 逆向工程分析器"""
    
    def __init__(self, page, logger=None):
        self.page = page
        self.logger = logger or logging.getLogger(__name__)
        self.detected_functions = {}
        self.network_requests = []
        self.analysis_active = False
        
    def inject_analysis_tools(self):
        """注入逆向分析工具到页面"""
        self.logger.log('🔧 注入 Verisoul 逆向分析工具...')
        
        analysis_script = """
        // Verisoul 逆向分析工具
        window.verisoulAnalyzer = {
            detectedFunctions: new Map(),
            networkRequests: [],
            isAnalyzing: false,
            
            startAnalysis: function() {
                console.log('🔍 开始 Verisoul 逆向分析...');
                this.isAnalyzing = true;
                this.interceptVerisoulObject();
                this.hookCriticalAPIs();
                this.monitorNetworkRequests();
                console.log('✅ Verisoul 逆向分析已启动');
            },
            
            interceptVerisoulObject: function() {
                const self = this;
                Object.defineProperty(window, 'Verisoul', {
                    get() { return this._verisoul; },
                    set(value) {
                        console.log('🎯 Verisoul 对象被创建:', value);
                        if (value && typeof value === 'object') {
                            self.analyzeVerisoulObject(value);
                        }
                        this._verisoul = value;
                    },
                    configurable: true
                });
            },
            
            analyzeVerisoulObject: function(verisoulObj) {
                console.log('📊 分析 Verisoul 对象结构...');
                const methods = Object.getOwnPropertyNames(verisoulObj);
                console.log('🔧 发现的方法:', methods);
                
                methods.forEach(methodName => {
                    if (typeof verisoulObj[methodName] === 'function') {
                        this.hookVerisoulMethod(verisoulObj, methodName);
                    }
                });
            },
            
            hookVerisoulMethod: function(obj, methodName) {
                const originalMethod = obj[methodName];
                const self = this;
                
                obj[methodName] = function(...args) {
                    console.log(`🎣 ${methodName} 被调用:`, args);
                    self.detectedFunctions.set(methodName, {
                        args: args,
                        timestamp: Date.now(),
                        callStack: new Error().stack
                    });
                    const result = originalMethod.apply(this, args);
                    console.log(`📤 ${methodName} 返回:`, result);
                    return result;
                };
            },
            
            hookCriticalAPIs: function() {
                console.log('🎣 Hook 关键 API...');
                this.hookNavigatorProperties();
                this.hookCanvasAPI();
                this.hookNetworkAPIs();
            },
            
            hookNavigatorProperties: function() {
                const sensitiveProps = ['userAgent', 'platform', 'language', 'hardwareConcurrency', 'deviceMemory', 'webdriver'];
                sensitiveProps.forEach(prop => {
                    if (prop in navigator) {
                        const originalValue = navigator[prop];
                        let accessCount = 0;
                        Object.defineProperty(navigator, prop, {
                            get() {
                                accessCount++;
                                console.log(`🔍 navigator.${prop} 被访问 (第${accessCount}次):`, originalValue);
                                return originalValue;
                            },
                            configurable: true
                        });
                    }
                });
            },
            
            hookCanvasAPI: function() {
                const originalGetContext = HTMLCanvasElement.prototype.getContext;
                HTMLCanvasElement.prototype.getContext = function(contextType, ...args) {
                    console.log('🎨 Canvas getContext 被调用:', contextType);
                    return originalGetContext.call(this, contextType, ...args);
                };
            },
            
            hookNetworkAPIs: function() {
                const originalFetch = window.fetch;
                const self = this;
                window.fetch = function(url, options) {
                    if (url.includes('verisoul.ai')) {
                        console.log('🌐 Verisoul 网络请求:', url, options);
                        self.networkRequests.push({
                            url: url,
                            options: options,
                            timestamp: Date.now(),
                            type: 'fetch'
                        });
                    }
                    return originalFetch.apply(this, arguments);
                };
            },
            
            generateAnalysisReport: function() {
                const report = {
                    timestamp: new Date().toISOString(),
                    detectedFunctions: Array.from(this.detectedFunctions.entries()),
                    networkRequests: this.networkRequests.slice(-10)
                };
                console.log('📋 Verisoul 分析报告:', report);
                return report;
            },
            
            stopAnalysis: function() {
                console.log('🛑 停止 Verisoul 逆向分析');
                this.isAnalyzing = false;
            }
        };
        
        console.log('🔍 Verisoul 逆向分析工具已注入');
        """
        
        try:
            self.page.run_js(analysis_script)
            self.logger.log('✅ Verisoul 逆向分析工具注入成功')
            return True
        except Exception as e:
            self.logger.error('❌ 注入逆向分析工具失败', e)
            return False
    
    def inject_countermeasures(self):
        """注入高级对抗措施"""
        self.logger.log('🛡️ 注入 Verisoul 高级对抗措施...')
        
        countermeasures_script = """
        // Verisoul 高级对抗措施
        window.verisoulCountermeasures = {
            isActive: false,
            
            activate: function() {
                console.log('🛡️ 启动 Verisoul 高级对抗措施...');
                this.isActive = true;
                this.applyPreventiveCountermeasures();
                this.applyInterceptiveCountermeasures();
                this.applyDeceptiveCountermeasures();
                this.applyBehavioralCountermeasures();
                console.log('✅ Verisoul 高级对抗措施已激活');
            },
            
            applyPreventiveCountermeasures: function() {
                console.log('🚫 应用预防性对抗措施...');
                this.presetEnvironmentVariables();
                this.hideAutomationFeatures();
            },
            
            presetEnvironmentVariables: function() {
                const fingerprint = this.generateConsistentFingerprint();
                
                Object.defineProperties(navigator, {
                    webdriver: { get: () => undefined, configurable: true },
                    hardwareConcurrency: { get: () => fingerprint.hardwareConcurrency, configurable: true },
                    deviceMemory: { get: () => fingerprint.deviceMemory, configurable: true },
                    maxTouchPoints: { get: () => fingerprint.maxTouchPoints, configurable: true },
                    platform: { get: () => fingerprint.platform, configurable: true },
                    language: { get: () => fingerprint.language, configurable: true },
                    languages: { get: () => fingerprint.languages, configurable: true }
                });
            },
            
            generateConsistentFingerprint: function() {
                const seed = Date.now().toString();
                const hash = this.simpleHash(seed);
                return {
                    hardwareConcurrency: 4 + (hash % 8),
                    deviceMemory: [4, 8, 16][hash % 3],
                    maxTouchPoints: hash % 2,
                    platform: ['Win32', 'MacIntel', 'Linux x86_64'][hash % 3],
                    language: ['en-US', 'en-GB', 'en-CA'][hash % 3],
                    languages: [['en-US', 'en'], ['en-GB', 'en'], ['en-CA', 'en']][hash % 3]
                };
            },
            
            simpleHash: function(str) {
                let hash = 0;
                for (let i = 0; i < str.length; i++) {
                    const char = str.charCodeAt(i);
                    hash = ((hash << 5) - hash) + char;
                    hash = hash & hash;
                }
                return Math.abs(hash);
            },
            
            hideAutomationFeatures: function() {
                delete navigator.__proto__.webdriver;
                delete window.navigator.webdriver;
                
                const seleniumVars = [
                    '__selenium_evaluate', '__selenium_unwrapped', '__webdriver_script_function',
                    '__webdriver_script_func', '__webdriver_script_fn', '__fxdriver_evaluate',
                    '__driver_unwrapped', '__webdriver_unwrapped', '__driver_evaluate'
                ];
                
                seleniumVars.forEach(varName => {
                    if (window[varName]) {
                        delete window[varName];
                    }
                });
            },
            
            applyInterceptiveCountermeasures: function() {
                console.log('🎣 应用拦截性对抗措施...');
                this.interceptVerisoulCreation();
            },
            
            interceptVerisoulCreation: function() {
                const self = this;
                Object.defineProperty(window, 'Verisoul', {
                    get() { return this._verisoul; },
                    set(value) {
                        console.log('🎯 拦截 Verisoul 对象创建');
                        if (value && typeof value === 'object') {
                            self.modifyVerisoulObject(value);
                        }
                        this._verisoul = value;
                    },
                    configurable: true
                });
            },
            
            modifyVerisoulObject: function(verisoulObj) {
                console.log('🔧 修改 Verisoul 对象方法...');
                if (verisoulObj.session) {
                    const originalSession = verisoulObj.session;
                    verisoulObj.session = async function(...args) {
                        console.log('🔐 拦截 session() 调用');
                        try {
                            const result = await originalSession.apply(this, args);
                            if (result && result.session_id) {
                                console.log('✅ Session 调用成功:', result.session_id);
                            }
                            return result;
                        } catch (error) {
                            console.log('❌ Session 调用失败，返回模拟结果');
                            return {
                                session_id: 'sim_' + Math.random().toString(36).substr(2, 9)
                            };
                        }
                    };
                }
            },
            
            applyDeceptiveCountermeasures: function() {
                console.log('🎭 应用欺骗性对抗措施...');
                this.spoofCanvasFingerprint();
                this.spoofWebGLFingerprint();
            },
            
            spoofCanvasFingerprint: function() {
                const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
                CanvasRenderingContext2D.prototype.getImageData = function(sx, sy, sw, sh) {
                    const imageData = originalGetImageData.call(this, sx, sy, sw, sh);
                    for (let i = 0; i < imageData.data.length; i += 4) {
                        if (Math.random() < 0.001) {
                            imageData.data[i] = Math.min(255, imageData.data[i] + Math.floor(Math.random() * 3) - 1);
                            imageData.data[i + 1] = Math.min(255, imageData.data[i + 1] + Math.floor(Math.random() * 3) - 1);
                            imageData.data[i + 2] = Math.min(255, imageData.data[i + 2] + Math.floor(Math.random() * 3) - 1);
                        }
                    }
                    return imageData;
                };
            },
            
            spoofWebGLFingerprint: function() {
                const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
                WebGLRenderingContext.prototype.getParameter = function(parameter) {
                    const result = originalGetParameter.call(this, parameter);
                    if (parameter === this.RENDERER) {
                        const renderers = [
                            'ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)',
                            'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11)',
                            'ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0, D3D11)'
                        ];
                        return renderers[Math.floor(Math.random() * renderers.length)];
                    }
                    return result;
                };
            },
            
            applyBehavioralCountermeasures: function() {
                console.log('🎭 应用行为性对抗措施...');
                this.simulateMouseBehavior();
                this.simulateScrollBehavior();
            },
            
            simulateMouseBehavior: function() {
                setInterval(() => {
                    if (!this.isActive) return;
                    const x = Math.random() * window.innerWidth;
                    const y = Math.random() * window.innerHeight;
                    const event = new MouseEvent('mousemove', {
                        clientX: x, clientY: y, bubbles: true, cancelable: true
                    });
                    document.dispatchEvent(event);
                }, 2000 + Math.random() * 3000);
            },
            
            simulateScrollBehavior: function() {
                setInterval(() => {
                    if (!this.isActive) return;
                    const scrollAmount = (Math.random() - 0.5) * 100;
                    window.scrollBy(0, scrollAmount);
                }, 5000 + Math.random() * 10000);
            },
            
            deactivate: function() {
                console.log('🛑 停用 Verisoul 高级对抗措施');
                this.isActive = false;
            },
            
            getStatus: function() {
                return {
                    isActive: this.isActive
                };
            }
        };
        
        console.log('🛡️ Verisoul 高级对抗系统已注入');
        """
        
        try:
            self.page.run_js(countermeasures_script)
            self.logger.log('✅ Verisoul 高级对抗措施注入成功')
            return True
        except Exception as e:
            self.logger.error('❌ 注入高级对抗措施失败', e)
            return False
    
    def start_analysis(self):
        """启动逆向分析"""
        self.logger.log('🔍 启动 Verisoul 逆向分析...')

        try:
            result = self.page.run_js('window.verisoulAnalyzer.startAnalysis(); return true;')
            if result:
                self.analysis_active = True
                self.logger.log('✅ Verisoul 逆向分析已启动')
                return True
        except Exception as e:
            self.logger.error('❌ 启动逆向分析失败', e)

        return False
    
    def activate_countermeasures(self):
        """激活对抗措施"""
        self.logger.log('🛡️ 激活 Verisoul 对抗措施...')

        try:
            result = self.page.run_js('window.verisoulCountermeasures.activate(); return true;')
            if result:
                self.logger.log('✅ Verisoul 对抗措施已激活')
                return True
        except Exception as e:
            self.logger.error('❌ 激活对抗措施失败', e)

        return False
    
    def generate_analysis_report(self) -> Optional[Dict]:
        """生成分析报告"""
        try:
            report = self.page.run_js('return window.verisoulAnalyzer.generateAnalysisReport();')
            if report:
                self.logger.log('📊 生成 Verisoul 分析报告成功')
                return report
        except Exception as e:
            self.logger.error('❌ 生成分析报告失败', e)

        return None
    
    def get_countermeasures_status(self) -> Optional[Dict]:
        """获取对抗措施状态"""
        try:
            status = self.page.run_js('return window.verisoulCountermeasures.getStatus();')
            return status
        except Exception as e:
            self.logger.error('❌ 获取对抗措施状态失败', e)
            return None
    
    def stop_analysis(self):
        """停止分析"""
        try:
            self.page.run_js('window.verisoulAnalyzer.stopAnalysis();')
            self.page.run_js('window.verisoulCountermeasures.deactivate();')
            self.analysis_active = False
            self.logger.log('🛑 Verisoul 逆向分析已停止')
        except Exception as e:
            self.logger.error('❌ 停止分析失败', e)
