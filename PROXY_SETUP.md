# 代理配置使用说明

## 🎯 您的代理配置

根据您提供的信息，您已经有了以下代理配置：

```
PROXY_URL=us2.cliproxy.io:3010
PROXY_USER=lovh89107-region-SG
PROXY_PASS=ebmjyzqo
```

这个配置支持 HTTP 和 SOCKS5 协议。

## 🚀 快速开始

### 1. 确认 .env 文件配置

确保您的 `.env` 文件包含以下内容：

```env
PROXY_URL=us2.cliproxy.io:3010
PROXY_USER=lovh89107-region-SG
PROXY_PASS=ebmjyzqo
```

### 2. 测试代理配置

运行测试脚本确认代理工作正常：

```bash
node test-proxy.js
```

这会显示：
- ✅ 代理配置详情
- 🔗 连通性测试结果
- 🌐 浏览器配置信息

### 3. 运行邮箱验证（使用代理）

现在运行您的邮箱验证脚本，它会自动使用代理：

```bash
node run-email-verification.js
```

## 📊 预期输出

运行时您会看到类似以下的输出：

```
🚀 开始 Augment 授权和邮箱验证流程

🔍 检查代理配置...
✅ 代理配置已找到
🌐 代理地址: us2.cliproxy.io:3010
👤 代理用户: lovh89107-region-SG
🔐 代理密码: 已设置

🔐 步骤1: 生成 Augment 授权 URL
✅ 授权 URL 已生成并设置

🔐 步骤2: 执行邮箱验证和授权流程
✅ 代理配置成功: us2.cliproxy.io:3010 (http)
🔐 代理认证: lovh89107-region-SG
🌐 浏览器代理参数: --proxy-server=http://us2.cliproxy.io:3010
🔐 页面代理认证已设置
```

## 🔧 代理配置选项

### 协议支持

您的代理支持两种协议，可以通过环境变量指定：

```env
# 使用 HTTP 协议 (默认)
PROXY_PROTOCOL=http

# 或使用 SOCKS5 协议
PROXY_PROTOCOL=socks5
```

### 完整配置示例

```env
# 代理服务器配置
PROXY_URL=us2.cliproxy.io:3010
PROXY_USER=lovh89107-region-SG
PROXY_PASS=ebmjyzqo
PROXY_PROTOCOL=http

# 其他配置
LINK_TO_TEST=https://example.com
```

## 🎯 解决浏览器指纹问题

使用代理后，每次运行都会：

1. **使用不同的出口IP** - 通过代理服务器的IP池
2. **保持会话稳定** - 10分钟内使用相同IP
3. **支持地理定位** - 您的代理配置为新加坡地区 (region-SG)
4. **认证安全** - 使用用户名密码认证

## 🔍 故障排除

### 如果代理测试失败

1. **检查网络连接**
   ```bash
   ping us2.cliproxy.io
   ```

2. **验证认证信息**
   - 确认用户名和密码正确
   - 检查代理服务是否到期

3. **尝试不同协议**
   ```env
   PROXY_PROTOCOL=socks5
   ```

### 如果浏览器启动失败

1. **检查代理参数**
   ```bash
   node test-proxy.js
   ```

2. **查看错误日志**
   - 检查 image/ 目录中的截图
   - 查看控制台错误信息

### 常见错误及解决方案

| 错误信息 | 原因 | 解决方案 |
|---------|------|---------|
| `代理配置不完整` | 环境变量缺失 | 检查 .env 文件 |
| `代理测试失败` | 网络连接问题 | 检查代理服务器状态 |
| `认证失败` | 用户名密码错误 | 验证认证信息 |
| `连接超时` | 代理服务器响应慢 | 尝试其他代理节点 |

## 📈 性能优化建议

1. **选择最近的代理节点** - 您当前使用的是新加坡节点
2. **监控代理使用量** - 避免超出流量限制
3. **定期轮换会话** - 每10分钟自动轮换
4. **备用代理配置** - 准备多个代理以防故障

## 🎉 成功指标

使用代理后，您应该看到：

- ✅ **成功率提升** - 不再出现连续失败
- 🌐 **IP地址变化** - 每次使用不同的出口IP
- 🔐 **认证成功** - 代理认证正常工作
- 📊 **稳定性改善** - 减少反自动化检测

现在您可以运行 `node run-email-verification.js` 来测试完整的流程！
