const axios = require('axios');

class CaptchaHandler {
    constructor() {
        this.yesCaptchaKey = 'b289ed1345b8af1fd74244a0438aac098f9a3b2376209';
        this.defaultSiteKey = '0x4AAAAAAABkMYinukE_rfkN';
        this.recaptchaSiteKey = '6LcoVhMrAAAAACg2fvNox_iH00SjOIWoewNh_PX1';
        // 强制使用最高分数模式：RecaptchaV3TaskProxylessM1S9 (35 points, 强制0.9分)
        console.log('[CAPTCHA] 🎯 已配置为强制使用 RecaptchaV3TaskProxylessM1S9 (35 points, 强制0.9分)');
    }

    // 处理Cloudflare Turnstile
    async handleTurnstile(page, websiteURL, siteKey) {
        try {
            console.log('[CAPTCHA] 处理Turnstile验证码...');
            console.log(`[CAPTCHA] URL: ${websiteURL}`);
            console.log(`[CAPTCHA] SiteKey: ${siteKey}`);

            const taskId = await this.createTurnstileTask(websiteURL, siteKey);
            if (!taskId) {
                console.log('[CAPTCHA] Turnstile任务创建失败');
                return false;
            }

            const token = await this.getCaptchaResult(taskId);
            if (!token) {
                console.log('[CAPTCHA] Turnstile token获取失败');
                return false;
            }

            console.log(`[CAPTCHA] 获取到token: ${token.substring(0, 50)}...`);

            const injectionResult = await this.injectTurnstileToken(page, token);
            console.log(`[CAPTCHA] Token注入结果: ${injectionResult}`);

            // 保存token到全局变量供调试使用
            global.lastCaptchaToken = token;
            global.lastInjectionResult = injectionResult;

            console.log('[CAPTCHA] ✅ Turnstile处理完成');
            return true;

        } catch (error) {
            console.log(`[CAPTCHA] Turnstile处理失败: ${error.message}`);
            return false;
        }
    }

    // 处理reCAPTCHA Enterprise - 自动提交页面专用
    async handleRecaptchaEnterprise(page) {
        try {
            console.log('[CAPTCHA] 处理reCAPTCHA Enterprise...');

            // 首先详细检测reCAPTCHA版本
            const recaptchaInfo = await this.detectRecaptchaVersion(page);

            // 检测是否为自动提交页面
            const isAutoSubmitPage = await page.evaluate(() => {
                return document.body.innerHTML.includes('Auto-submit logic') ||
                       document.body.innerHTML.includes('onClick') ||
                       document.querySelector('#action-form');
            });

            if (isAutoSubmitPage) {
                console.log('[CAPTCHA] 检测到自动提交页面，使用YesCaptcha解决方案...');
                return await this.handleAutoSubmitRecaptcha(page);
            }

            // 原有的处理逻辑
            const hasRecaptcha = await page.evaluate(() => {
                return !!(window.grecaptcha && window.grecaptcha.enterprise);
            });

            if (!hasRecaptcha) {
                console.log('[CAPTCHA] 未检测到reCAPTCHA Enterprise');
                return false;
            }

            // 执行reCAPTCHA
            const token = await page.evaluate(async (siteKey) => {
                try {
                    if (window.grecaptcha && window.grecaptcha.enterprise) {
                        const token = await window.grecaptcha.enterprise.execute(siteKey, {
                            action: 'submit'
                        });
                        return token;
                    }
                    return null;
                } catch (error) {
                    console.error('reCAPTCHA执行失败:', error);
                    return null;
                }
            }, this.recaptchaSiteKey);

            if (token) {
                await this.injectRecaptchaToken(page, token);
                console.log('[CAPTCHA] ✅ reCAPTCHA Enterprise处理完成');
                return true;
            } else {
                console.log('[CAPTCHA] reCAPTCHA Enterprise token获取失败');
                return false;
            }

        } catch (error) {
            console.log(`[CAPTCHA] reCAPTCHA Enterprise处理失败: ${error.message}`);
            return false;
        }
    }

    // 处理自动提交页面的reCAPTCHA - 精确时机控制
    async handleAutoSubmitRecaptcha(page) {
        try {
            console.log('[CAPTCHA] 🎯 开始处理自动提交页面的reCAPTCHA...');

            // 新策略：延迟接管 - 让reCAPTCHA先收集行为数据，再替换token
            await page.evaluateOnNewDocument(() => {
                window.__ycToken = null;
                window.__captchaCollectionComplete = false;

                // 延迟接管策略：让页面正常运行3-5秒收集行为数据
                setTimeout(() => {
                    console.log('[CAPTCHA] 🕐 开始延迟接管reCAPTCHA...');

                    // 保存原始的onClick函数
                    if (typeof window.onClick === 'function') {
                        window._originalOnClick = window.onClick;

                        // 替换onClick函数
                        window.onClick = async function(e) {
                            console.log('[CAPTCHA] 🎯 接管onClick，使用YesCaptcha token');
                            e.preventDefault();

                            try {
                                // 等待YesCaptcha token准备好
                                while (!window.__ycToken) {
                                    console.log('[CAPTCHA] 等待YesCaptcha token...');
                                    await new Promise(resolve => setTimeout(resolve, 500));
                                }

                                // 让Verisoul正常执行
                                const verisoulPromise = window.Verisoul ?
                                    window.Verisoul.session().catch(e => ({ session_id: '' })) :
                                    Promise.resolve({ session_id: '' });

                                // 使用我们的token而不是调用grecaptcha
                                const grecaptchaPromise = Promise.resolve(window.__ycToken);
                                const verosintPromise = Promise.resolve('');

                                const [verisoulResponse, grecaptchaResponse, verosintResponse] = await Promise.allSettled([
                                    verisoulPromise,
                                    grecaptchaPromise,
                                    verosintPromise
                                ]);

                                // 填充表单
                                if (verisoulResponse.status === 'fulfilled') {
                                    const verisoulInput = document.getElementById('verisoul-session-id');
                                    if (verisoulInput) {
                                        verisoulInput.value = verisoulResponse.value.session_id || '';
                                    }
                                }

                                if (grecaptchaResponse.status === 'fulfilled') {
                                    const recaptchaInput = document.getElementById('g-recaptcha-response');
                                    if (recaptchaInput) {
                                        recaptchaInput.value = grecaptchaResponse.value;
                                    }
                                }

                                if (verosintResponse.status === 'fulfilled') {
                                    const verosintInput = document.getElementById('verosint-deviceid');
                                    if (verosintInput) {
                                        verosintInput.value = verosintResponse.value;
                                    }
                                }

                                // 清空client-errors
                                const errorsInput = document.getElementById('client-errors');
                                if (errorsInput) {
                                    errorsInput.value = JSON.stringify([]);
                                }

                                // 提交表单
                                const form = document.getElementById('action-form');
                                if (form) {
                                    console.log('[CAPTCHA] 🚀 提交表单 (延迟接管模式)');
                                    form.submit();
                                } else {
                                    console.log('[CAPTCHA] ❌ 未找到表单');
                                }

                            } catch (error) {
                                console.log('[CAPTCHA] ❌ 延迟接管执行失败:', error);
                                // 回退到原始函数
                                if (window._originalOnClick) {
                                    return window._originalOnClick.call(this, e);
                                }
                            }
                        };

                        console.log('[CAPTCHA] ✅ onClick函数已被延迟接管');
                    }

                    window.__captchaCollectionComplete = true;
                }, 3000); // 延迟3秒，让reCAPTCHA收集行为数据
            });

            // 第二步：检测pageAction并获取YesCaptcha token（并行进行）
            console.log('[CAPTCHA] 🔍 检测pageAction值...');
            const pageAction = await this.detectPageAction(page);

            console.log('[CAPTCHA] 🔄 使用YesCaptcha获取reCAPTCHA token...');
            const currentUrl = page.url();
            const taskId = await this.createRecaptchaTask(currentUrl, this.recaptchaSiteKey, pageAction);

            if (!taskId) {
                console.log('[CAPTCHA] ❌ reCAPTCHA任务创建失败');
                return false;
            }

            const token = await this.getRecaptchaResult(taskId);
            if (!token) {
                console.log('[CAPTCHA] ❌ reCAPTCHA token获取失败');
                return false;
            }

            console.log(`[CAPTCHA] ✅ 获取到reCAPTCHA token: ${token.substring(0, 50)}...`);

            // 第三步：将token注入到页面，供延迟接管使用
            await page.evaluate((token) => {
                window.__ycToken = token;
                console.log('[CAPTCHA] YesCaptcha token已注入到页面');
            }, token);

            // 第四步：等待延迟接管完成并监控结果
            console.log('[CAPTCHA] ⏳ 等待延迟接管完成...');

            // 等待延迟接管机制生效（3秒延迟 + 处理时间）
            await this.wait(5000);

            // 检查是否成功接管
            const interceptResult = await page.evaluate(() => {
                return {
                    hasToken: !!window.__ycToken,
                    collectionComplete: !!window.__captchaCollectionComplete,
                    onClickReplaced: typeof window.onClick === 'function' && !!window._originalOnClick
                };
            });

            console.log('[CAPTCHA] 延迟接管状态:', interceptResult);

            if (interceptResult.hasToken && interceptResult.collectionComplete) {
                console.log('[CAPTCHA] ✅ 延迟接管已就绪，等待自动提交...');

                // 等待页面自动提交（由延迟接管的onClick处理）
                await this.wait(8000);

                console.log('[CAPTCHA] ✅ 自动提交页面处理成功 (延迟接管模式)');
                return true;
            } else {
                console.log('[CAPTCHA] ❌ 延迟接管未能正确设置');
                return false;
            }

        } catch (error) {
            console.log(`[CAPTCHA] 自动提交页面处理异常: ${error.message}`);
            return false;
        }
    }

    // 详细检测reCAPTCHA版本和类型
    async detectRecaptchaVersion(page) {
        try {
            const recaptchaInfo = await page.evaluate(() => {
                const info = {
                    version: 'unknown',
                    isEnterprise: false,
                    siteKey: null,
                    hasV2: false,
                    hasV3: false,
                    evidence: []
                };

                // 检查是否有grecaptcha对象
                if (window.grecaptcha) {
                    info.evidence.push('window.grecaptcha exists');

                    // 检查Enterprise
                    if (window.grecaptcha.enterprise) {
                        info.isEnterprise = true;
                        info.evidence.push('window.grecaptcha.enterprise exists');
                    }

                    // 检查v2方法
                    if (window.grecaptcha.render) {
                        info.hasV2 = true;
                        info.evidence.push('grecaptcha.render method exists (v2)');
                    }

                    // 检查v3方法
                    if (window.grecaptcha.execute) {
                        info.hasV3 = true;
                        info.evidence.push('grecaptcha.execute method exists (v3)');
                    }
                }

                // 检查页面中的reCAPTCHA元素
                const v2Elements = document.querySelectorAll('.g-recaptcha');
                if (v2Elements.length > 0) {
                    info.hasV2 = true;
                    info.evidence.push(`Found ${v2Elements.length} .g-recaptcha elements (v2)`);
                }

                // 检查隐藏的response字段
                const responseInputs = document.querySelectorAll('#g-recaptcha-response, input[name="g-recaptcha-response"]');
                if (responseInputs.length > 0) {
                    info.evidence.push(`Found ${responseInputs.length} g-recaptcha-response inputs`);
                }

                // 检查site key
                const siteKeyElements = document.querySelectorAll('[data-sitekey]');
                if (siteKeyElements.length > 0) {
                    const siteKey = siteKeyElements[0].getAttribute('data-sitekey');
                    if (siteKey && siteKey.startsWith('6L')) {
                        info.siteKey = siteKey;
                        info.evidence.push(`Found site key: ${siteKey}`);
                    }
                }

                // 检查页面源码中的Enterprise调用
                const scripts = Array.from(document.querySelectorAll('script'));
                for (const script of scripts) {
                    const content = script.textContent || script.innerHTML;
                    if (content.includes('grecaptcha.enterprise.execute')) {
                        info.isEnterprise = true;
                        info.hasV3 = true;
                        info.evidence.push('Found grecaptcha.enterprise.execute in scripts');
                    }
                    if (content.includes('grecaptcha.execute')) {
                        info.hasV3 = true;
                        info.evidence.push('Found grecaptcha.execute in scripts');
                    }
                }

                // 确定版本
                if (info.hasV3 && info.isEnterprise) {
                    info.version = 'v3-enterprise';
                } else if (info.hasV3) {
                    info.version = 'v3';
                } else if (info.hasV2) {
                    info.version = 'v2';
                }

                return info;
            });

            console.log('[CAPTCHA] 🔍 reCAPTCHA版本检测结果:');
            console.log(`[CAPTCHA]   版本: ${recaptchaInfo.version}`);
            console.log(`[CAPTCHA]   Enterprise: ${recaptchaInfo.isEnterprise}`);
            console.log(`[CAPTCHA]   Site Key: ${recaptchaInfo.siteKey || 'Not found'}`);
            console.log(`[CAPTCHA]   证据:`);
            recaptchaInfo.evidence.forEach(evidence => {
                console.log(`[CAPTCHA]     - ${evidence}`);
            });

            return recaptchaInfo;
        } catch (error) {
            console.log(`[CAPTCHA] ❌ reCAPTCHA版本检测失败: ${error.message}`);
            return { version: 'unknown', isEnterprise: false, siteKey: null, evidence: [] };
        }
    }

    // 动态检测pageAction值
    async detectPageAction(page) {
        try {
            const pageAction = await page.evaluate(() => {
                // 方法1: 检查grecaptcha.execute调用
                const scripts = Array.from(document.querySelectorAll('script'));
                for (const script of scripts) {
                    const content = script.textContent || script.innerHTML;
                    const actionMatch = content.match(/grecaptcha\.enterprise\.execute\([^,]+,\s*{\s*action:\s*['"]([^'"]+)['"]/);
                    if (actionMatch) {
                        return actionMatch[1];
                    }
                }

                // 方法2: 检查data-action属性
                const recaptchaElements = document.querySelectorAll('[data-action]');
                if (recaptchaElements.length > 0) {
                    return recaptchaElements[0].getAttribute('data-action');
                }

                // 方法3: 检查常见的action值
                const commonActions = ['submit', 'signup', 'login', 'verify', 'homepage'];
                for (const action of commonActions) {
                    if (document.body.innerHTML.includes(`action: '${action}'`) ||
                        document.body.innerHTML.includes(`action: "${action}"`)) {
                        return action;
                    }
                }

                return 'submit'; // 默认值
            });

            console.log(`[CAPTCHA] 🔍 检测到pageAction: ${pageAction}`);
            return pageAction;
        } catch (error) {
            console.log(`[CAPTCHA] ⚠️ pageAction检测失败，使用默认值: ${error.message}`);
            return 'submit';
        }
    }

    // 创建reCAPTCHA任务 - 使用标准YesCaptcha API
    async createRecaptchaTask(websiteURL, siteKey, pageAction = null) {
        try {
            console.log(`[CAPTCHA] 🔄 调用YesCaptcha标准API创建reCAPTCHA v3任务...`);

            // 如果没有提供pageAction，使用默认值
            const action = pageAction || 'submit';

            // 强制使用最高分数模式：RecaptchaV3TaskProxylessM1S9
            const taskTypes = [
                { type: 'RecaptchaV3TaskProxylessM1S9', points: 35, desc: 'M1架构-强制0.9分' }
            ];

            console.log('[CAPTCHA] 🎯 强制使用最高分数模式: RecaptchaV3TaskProxylessM1S9 (35 points, 强制0.9分)');

            for (const taskType of taskTypes) {
                console.log(`[CAPTCHA] 🎯 尝试使用 ${taskType.desc} (${taskType.points} points)`);

                const response = await axios.post('https://api.yescaptcha.com/createTask', {
                    clientKey: this.yesCaptchaKey,
                    task: {
                        type: taskType.type,
                        websiteURL: websiteURL,
                        websiteKey: siteKey,
                        // minScore 不需要指定，RecaptchaV3TaskProxylessM1S9 强制使用0.9分
                        pageAction: action
                    }
                });

                console.log(`[CAPTCHA] 🔍 创建任务响应:`, JSON.stringify(response.data, null, 2));

                if (response.data.errorId === 0) {
                    console.log(`[CAPTCHA] ✅ reCAPTCHA任务创建成功: ${response.data.taskId} (${taskType.desc})`);
                    return response.data.taskId;
                } else {
                    console.log(`[CAPTCHA] ❌ ${taskType.desc} 创建失败: ${response.data.errorDescription}`);
                    // 继续尝试下一个type
                }
            }

            console.log(`[CAPTCHA] ❌ 所有reCAPTCHA任务类型都创建失败`);
            return null;
        } catch (error) {
            console.log(`[CAPTCHA] 创建reCAPTCHA任务失败: ${error.message}`);
            if (error.response) {
                console.log(`[CAPTCHA] 错误响应:`, error.response.data);
            }
            return null;
        }
    }

    // 获取reCAPTCHA结果 - 使用标准YesCaptcha API
    async getRecaptchaResult(taskId) {
        try {
            const startTime = Date.now();
            let checkCount = 0;

            while (Date.now() - startTime < 80000) {
                checkCount++;
                const elapsed = Math.floor((Date.now() - startTime) / 1000);

                if (checkCount % 20 === 1) {
                    console.log(`[CAPTCHA] 处理中... (${elapsed}s)`);
                }

                const response = await axios.post('https://api.yescaptcha.com/getTaskResult', {
                    clientKey: this.yesCaptchaKey,
                    taskId: taskId
                });

                // 添加调试信息 - 只在第一次显示完整响应
                if (checkCount === 1) {
                    console.log(`[CAPTCHA] 🔍 YesCaptcha API完整响应:`, JSON.stringify(response.data, null, 2));
                }

                if (response.data.errorId > 0) {
                    console.log(`[CAPTCHA] 获取结果失败: ${response.data.errorDescription}`);
                    return null;
                }

                if (response.data.status === 'ready') {
                    console.log('[CAPTCHA] ✅ 验证码解决成功');

                    // 处理不同类型验证码的响应格式
                    const solution = response.data.solution;
                    if (solution) {
                        // reCAPTCHA v3 - 直接返回token字符串或在gRecaptchaResponse中
                        if (solution.gRecaptchaResponse) {
                            console.log(`[CAPTCHA] 获取到reCAPTCHA token (gRecaptchaResponse): ${solution.gRecaptchaResponse.substring(0, 50)}...`);
                            return solution.gRecaptchaResponse;
                        }
                        // Turnstile - 在token字段中
                        if (solution.token) {
                            console.log(`[CAPTCHA] 获取到token: ${solution.token.substring(0, 50)}...`);
                            return solution.token;
                        }
                        // 如果solution是字符串，直接返回
                        if (typeof solution === 'string') {
                            console.log(`[CAPTCHA] 获取到token (字符串): ${solution.substring(0, 50)}...`);
                            return solution;
                        }

                        console.log('[CAPTCHA] ⚠️ 未知的solution格式:', JSON.stringify(solution));
                        // 尝试返回solution的第一个值
                        const firstValue = Object.values(solution)[0];
                        if (typeof firstValue === 'string') {
                            console.log(`[CAPTCHA] 尝试使用第一个值: ${firstValue.substring(0, 50)}...`);
                            return firstValue;
                        }
                    }

                    console.log('[CAPTCHA] ❌ solution格式无法识别');
                    return null;
                }

                if (response.data.status === 'processing') {
                    await this.wait(3000);
                }
            }

            console.log('[CAPTCHA] ❌ 验证码处理超时');
            return null;
        } catch (error) {
            console.log(`[CAPTCHA] 获取reCAPTCHA结果失败: ${error.message}`);
            if (error.response) {
                console.log(`[CAPTCHA] 错误响应:`, error.response.data);
            }
            return null;
        }
    }

    // 创建Turnstile任务
    async createTurnstileTask(websiteURL, siteKey) {
        try {
            const response = await axios.post('https://api.yescaptcha.com/createTask', {
                clientKey: this.yesCaptchaKey,
                task: {
                    type: 'TurnstileTaskProxyless',
                    websiteURL: websiteURL,
                    websiteKey: siteKey
                }
            });

            if (response.data.errorId === 0) {
                console.log(`[CAPTCHA] 任务创建成功: ${response.data.taskId}`);
                return response.data.taskId;
            } else {
                console.log(`[CAPTCHA] 任务创建失败: ${response.data.errorDescription}`);
                return null;
            }
        } catch (error) {
            console.log(`[CAPTCHA] 创建任务失败: ${error.message}`);
            return null;
        }
    }

    // 获取验证码结果
    async getCaptchaResult(taskId) {
        try {
            const startTime = Date.now();
            let checkCount = 0;

            while (Date.now() - startTime < 80000) {
                checkCount++;
                const elapsed = Math.floor((Date.now() - startTime) / 1000);

                if (checkCount % 20 === 1) {
                    console.log(`[CAPTCHA] 处理中... (${elapsed}s)`);
                }

                const response = await axios.post('https://api.yescaptcha.com/getTaskResult', {
                    clientKey: this.yesCaptchaKey,
                    taskId: taskId
                });

                if (response.data.errorId > 0) {
                    console.log(`[CAPTCHA] 获取结果失败: ${response.data.errorDescription}`);
                    return null;
                }

                // 添加调试信息 - 只在第一次显示完整响应
                if (checkCount === 1) {
                    console.log(`[CAPTCHA] 🔍 YesCaptcha API完整响应:`, JSON.stringify(response.data, null, 2));
                }

                // 添加调试信息
                if (checkCount === 1) {
                    console.log(`[CAPTCHA] 🔍 调试 - API响应格式:`, JSON.stringify(response.data, null, 2));
                }

                if (response.data.status === 'ready') {
                    console.log('[CAPTCHA] ✅ 验证码解决成功');

                    // 处理不同类型验证码的响应格式
                    const solution = response.data.solution;
                    if (solution) {
                        // reCAPTCHA v3 - 直接返回token字符串或在gRecaptchaResponse中
                        if (solution.gRecaptchaResponse) {
                            console.log(`[CAPTCHA] 获取到reCAPTCHA token (gRecaptchaResponse): ${solution.gRecaptchaResponse.substring(0, 50)}...`);
                            return solution.gRecaptchaResponse;
                        }
                        // Turnstile - 在token字段中
                        if (solution.token) {
                            console.log(`[CAPTCHA] 获取到token: ${solution.token.substring(0, 50)}...`);
                            return solution.token;
                        }
                        // 如果solution是字符串，直接返回
                        if (typeof solution === 'string') {
                            console.log(`[CAPTCHA] 获取到token (字符串): ${solution.substring(0, 50)}...`);
                            return solution;
                        }

                        console.log('[CAPTCHA] ⚠️ 未知的solution格式:', JSON.stringify(solution));
                        // 尝试返回solution的第一个值
                        const firstValue = Object.values(solution)[0];
                        if (typeof firstValue === 'string') {
                            console.log(`[CAPTCHA] 尝试使用第一个值: ${firstValue.substring(0, 50)}...`);
                            return firstValue;
                        }
                    }

                    console.log('[CAPTCHA] ❌ solution格式无法识别');
                    return null;
                }

                if (response.data.status === 'processing') {
                    await this.wait(3000);
                }
            }

            console.log('[CAPTCHA] ❌ 验证码处理超时');
            return null;
        } catch (error) {
            console.log(`[CAPTCHA] 获取结果失败: ${error.message}`);
            return null;
        }
    }

    // 注入Turnstile token
    async injectTurnstileToken(page, token) {
        try {
            const injectionResult = await page.evaluate((token) => {
                const result = {
                    inputsFound: [],
                    inputsInjected: 0,
                    callbacksTriggered: 0,
                    globalVariableSet: false,
                    turnstileApiAvailable: false
                };

                // 首先尝试找到现有的Turnstile响应字段
                let turnstileInput = document.querySelector('input[name="cf-turnstile-response"]');

                if (!turnstileInput) {
                    // 如果没有找到，创建一个
                    turnstileInput = document.createElement('input');
                    turnstileInput.type = 'hidden';
                    turnstileInput.name = 'cf-turnstile-response';

                    // 尝试找到form并添加到其中
                    const form = document.querySelector('form');
                    if (form) {
                        form.appendChild(turnstileInput);
                        result.inputsFound.push({
                            selector: 'cf-turnstile-response',
                            count: 1,
                            created: true
                        });
                    } else {
                        // 如果没有form，添加到body
                        document.body.appendChild(turnstileInput);
                        result.inputsFound.push({
                            selector: 'cf-turnstile-response',
                            count: 1,
                            created: true,
                            addedToBody: true
                        });
                    }
                } else {
                    result.inputsFound.push({
                        selector: 'cf-turnstile-response',
                        count: 1,
                        created: false
                    });
                }

                // 注入token到Turnstile响应字段
                if (turnstileInput) {
                    turnstileInput.value = token;
                    result.inputsInjected++;
                }

                // 也注入到其他可能的字段
                const otherSelectors = [
                    'input[name*="turnstile"]',
                    'input[name*="captcha"]'
                ];

                otherSelectors.forEach(selector => {
                    const inputs = document.querySelectorAll(selector);
                    result.inputsFound.push({
                        selector: selector,
                        count: inputs.length
                    });

                    inputs.forEach(input => {
                        input.value = token;
                        result.inputsInjected++;
                    });
                });

                // 检查Turnstile API
                result.turnstileApiAvailable = !!(window.turnstile && window.turnstile.render);

                // 触发Turnstile回调 - 多种方式
                if (window.turnstile) {
                    // 方式1: 查找带有data-sitekey的元素
                    const widgets = document.querySelectorAll('[data-sitekey]');
                    widgets.forEach((widget, index) => {
                        // 尝试多种回调方式
                        if (widget._turnstileCallback) {
                            widget._turnstileCallback(token);
                            result.callbacksTriggered++;
                        }

                        // 尝试通过data属性找到回调
                        const callbackName = widget.getAttribute('data-callback');
                        if (callbackName && window[callbackName]) {
                            window[callbackName](token);
                            result.callbacksTriggered++;
                        }

                        // 尝试触发change事件
                        const event = new Event('change', { bubbles: true });
                        widget.dispatchEvent(event);
                    });

                    // 方式2: 如果有turnstile.getResponse，尝试设置响应
                    if (window.turnstile.getResponse) {
                        try {
                            // 尝试找到turnstile实例并设置响应
                            const turnstileWidgets = document.querySelectorAll('.cf-turnstile');
                            turnstileWidgets.forEach(widget => {
                                if (widget.id) {
                                    try {
                                        // 模拟turnstile完成
                                        const event = new CustomEvent('cf-turnstile-response', {
                                            detail: { token: token }
                                        });
                                        widget.dispatchEvent(event);
                                        result.callbacksTriggered++;
                                    } catch (e) { }
                                }
                            });
                        } catch (e) { }
                    }

                    // 方式3: 触发全局事件
                    try {
                        const globalEvent = new CustomEvent('turnstile-callback', {
                            detail: { token: token }
                        });
                        document.dispatchEvent(globalEvent);
                        result.callbacksTriggered++;
                    } catch (e) { }
                }

                // 存储到全局变量
                window.__turnstileToken = token;
                result.globalVariableSet = true;

                return result;
            }, token);

            console.log(`[CAPTCHA] 注入详情: ${JSON.stringify(injectionResult, null, 2)}`);

            // 等待更长时间让页面处理token
            console.log('[CAPTCHA] 等待页面处理token...');
            await this.wait(5000);
            return injectionResult;

        } catch (error) {
            console.log(`[CAPTCHA] 注入Turnstile token失败: ${error.message}`);
            return { error: error.message };
        }
    }

    // 注入reCAPTCHA token
    async injectRecaptchaToken(page, token) {
        try {
            await page.evaluate((token) => {
                // 注入到reCAPTCHA响应字段
                let input = document.querySelector('input[name="g-recaptcha-response"]');
                if (!input) {
                    input = document.createElement('input');
                    input.type = 'hidden';
                    input.name = 'g-recaptcha-response';
                    const form = document.querySelector('form');
                    if (form) {
                        form.appendChild(input);
                    }
                }
                input.value = token;

                // 存储到全局变量
                window.__recaptchaToken = token;

                return true;
            }, token);

            await this.wait(1000);
            return true;

        } catch (error) {
            console.log(`[CAPTCHA] 注入reCAPTCHA token失败: ${error.message}`);
            return false;
        }
    }

    // 启用高分数模式 (使用强制0.7或0.9分的type)
    enableHighScoreMode() {
        this.useHighScoreMode = true;
        console.log('[CAPTCHA] ✅ 已启用高分数模式 (将使用强制高分的reCAPTCHA type)');
    }

    // 禁用高分数模式
    disableHighScoreMode() {
        this.useHighScoreMode = false;
        console.log('[CAPTCHA] ✅ 已禁用高分数模式 (使用标准reCAPTCHA type)');
    }

    // 辅助方法
    async wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

module.exports = CaptchaHandler;

