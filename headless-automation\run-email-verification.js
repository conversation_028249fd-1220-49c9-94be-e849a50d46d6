const AutoRegister = require('./index.js');
const AugmentAuth = require('./augment-auth.js');
const TokenStorage = require('./token-storage.js');
const FlowLogger = require('./flow-logger.js');
require('dotenv').config();

async function runEmailVerification() {
    const autoRegister = new AutoRegister();
    const augmentAuth = new AugmentAuth();
    const tokenStorage = new TokenStorage();
    const flowLogger = new FlowLogger();

    const startTime = Date.now();

    try {
        console.log('🚀 开始 Augment 授权和邮箱验证流程');
        console.log('');

        // 检查代理配置
        console.log('🔍 检查代理配置...');
        const proxyStatus = autoRegister.proxyHandler.getProxyStatus();
        if (process.env.PROXY_URL && process.env.PROXY_USER && process.env.PROXY_PASS) {
            console.log('✅ 代理配置已找到');
            console.log(`🌐 代理地址: ${process.env.PROXY_URL}`);
            console.log(`👤 代理用户: ${process.env.PROXY_USER}`);
            console.log('🔐 代理密码: 已设置');
        } else {
            console.log('⚠️ 未检测到完整的代理配置');
            console.log('💡 如需使用代理，请在 .env 文件中设置:');
            console.log('   PROXY_URL=host:port');
            console.log('   PROXY_USER=username');
            console.log('   PROXY_PASS=password');
        }
        console.log('');

        // 步骤1: 生成 Augment 授权 URL
        console.log('🔐 步骤1: 生成 Augment 授权 URL');
        const authUrl = augmentAuth.generateAuthUrl();

        // 将生成的 URL 设置为环境变量，替换硬编码的 LINK_TO_TEST
        process.env.LINK_TO_TEST = authUrl;

        console.log('✅ 授权 URL 已生成并设置');
        console.log(`📧 目标URL: ${authUrl}`);
        console.log('📝 使用One Mail API生成临时邮箱');
        console.log('⏰ 将自动获取验证码（2分钟超时，每5秒检查一次）');
        console.log('📸 每一步都会自动截图和保存HTML');
        console.log('');

        // 记录流程开始
        flowLogger.logFlowStart();

        // 步骤1.5: 初始化代理并检查当前IP信息
        console.log('🌐 检查当前IP信息...');
        const ProxyHandler = require('./proxy.js');
        const proxyHandler = new ProxyHandler();

        // 先获取代理配置（如果启用的话）
        await proxyHandler.getValidProxy();

        // 然后检查IP信息（会使用代理配置）
        const ipInfo = await proxyHandler.checkCurrentIP();

        if (ipInfo) {
            console.log('📊 IP分析结果:');
            if (ipInfo.type === 'hosting' || ipInfo.isp?.toLowerCase().includes('datacenter')) {
                console.log('⚠️  警告: 检测到数据中心IP，可能影响验证成功率');
                console.log('💡 建议: 启用住宅代理 (HEADLESS_PROXY=true)');
            } else {
                console.log('✅ IP类型看起来正常');
            }
        }

        // 步骤2: 执行邮箱验证流程（会使用我们生成的授权URL）
        console.log('🔐 步骤2: 执行邮箱验证和授权流程');

        // 启用高分数reCAPTCHA模式和增强反检测
        autoRegister.enableHighScoreCaptchaMode();
        console.log('🎯 已启用最高分数reCAPTCHA模式和增强反检测');

        const clipboardContent = await autoRegister.handleEmailVerificationWithOneMailAPI(authUrl);

        // 步骤3: 处理真实授权码并获取真实访问令牌
        if (clipboardContent) {
            console.log('');
            console.log('🔐 步骤3: 处理真实授权码并获取真实访问令牌');
            console.log('📋 从剪贴板获取到的真实授权码:');
            console.log(clipboardContent);
            console.log('');

            try {
                // 完成真实的 OAuth 流程 - 调用真实的 Augment API
                console.log('🚀 开始调用真实的 Augment API...');
                const tokenResponse = await augmentAuth.completeOAuthFlow(clipboardContent);

                console.log('');
                console.log('✅ 真实 API 调用成功！获取到真实访问令牌！');
                console.log(`🔑 真实访问令牌: ${tokenResponse.access_token?.substring(0, 30)}...`);
                console.log(`🏢 租户 URL: ${tokenResponse.tenant_url}`);

                // 保存真实令牌到 JSON 文件
                const tokenId = tokenStorage.addToken(tokenResponse, {
                    description: 'Real token from Augment API via email verification',
                    user_agent: 'augment-auto-email-verification',
                    session_id: `session_${Date.now()}`
                });

                console.log('');
                console.log('🎉 完整的真实流程成功完成！');
                console.log(`💾 真实令牌已保存到 tokens.json，ID: ${tokenId}`);
                console.log('📊 令牌统计:');
                tokenStorage.getStats();

                // 记录成功
                const duration = Date.now() - startTime;
                flowLogger.logFlowSuccess(duration);
                flowLogger.showStats();

            } catch (tokenError) {
                console.error('');
                console.error('❌ 真实 API 调用失败:', tokenError.message);
                console.error('📋 剪贴板内容已保存，可手动处理');
                console.error('🔍 错误详情:', tokenError.stack);

                // 记录令牌获取失败
                const duration = Date.now() - startTime;
                flowLogger.logFlowFailure(tokenError, duration);
            }
        } else {
            // 没有获取到剪贴板内容，记录失败
            const duration = Date.now() - startTime;
            const error = new Error('未获取到剪贴板内容，验证流程失败');
            flowLogger.logFlowFailure(error, duration);
        }

        console.log('');
        console.log('🎉 邮箱验证流程完成！');
        console.log('📁 请查看 image/ 目录中的截图和HTML文件');
        console.log('📁 请查看 tokens.json 文件中的令牌数据');
        console.log('📁 请查看 logs/ 目录中的详细日志文件');

    } catch (error) {
        console.error('');
        console.error('💥 邮箱验证流程失败:', error.message);
        console.error('📁 请查看 image/ 目录中的错误截图和HTML文件进行调试');
        console.error('📁 请查看 logs/ 目录中的详细错误日志');

        // 记录流程失败
        const duration = Date.now() - startTime;
        flowLogger.logFlowFailure(error, duration);
        flowLogger.showStats();

        process.exit(1);
    }
}

if (require.main === module) {
    runEmailVerification();
}
