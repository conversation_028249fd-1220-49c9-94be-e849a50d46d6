const AutoRegister = require('./headless-automation/index.js');

async function testBrowserProxy() {
    console.log('🔍 测试浏览器代理配置...');
    console.log('='.repeat(50));
    
    const autoRegister = new AutoRegister();
    
    try {
        // 初始化浏览器（会自动配置代理）
        console.log('🚀 启动浏览器...');
        await autoRegister.initBrowser();
        
        console.log('');
        console.log('🌐 检查浏览器IP信息...');
        
        // 访问IP检查页面
        await autoRegister.page.goto('https://httpbin.org/ip', { 
            waitUntil: 'networkidle2',
            timeout: 30000 
        });
        
        // 获取页面内容
        const content = await autoRegister.page.content();
        console.log('📄 页面内容:', content);
        
        // 尝试提取IP信息
        const ipMatch = content.match(/"origin":\s*"([^"]+)"/);
        if (ipMatch) {
            const browserIP = ipMatch[1];
            console.log(`🌐 浏览器显示的IP: ${browserIP}`);
            
            // 检查多个IP查询服务
            const ipServices = [
                'https://api.ipify.org?format=json',
                'https://ipapi.co/json/',
                'http://ip-api.com/json/'
            ];
            
            for (const service of ipServices) {
                try {
                    console.log(`\n🔍 检查服务: ${service}`);
                    await autoRegister.page.goto(service, { 
                        waitUntil: 'networkidle2',
                        timeout: 15000 
                    });
                    
                    const serviceContent = await autoRegister.page.content();
                    console.log(`📄 响应: ${serviceContent.substring(0, 200)}...`);
                    
                } catch (error) {
                    console.log(`❌ 服务 ${service} 失败: ${error.message}`);
                }
            }
        }
        
        // 截图保存
        await autoRegister.page.screenshot({ 
            path: 'proxy-test-screenshot.png',
            fullPage: true 
        });
        console.log('📸 截图已保存: proxy-test-screenshot.png');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    } finally {
        if (autoRegister.browser) {
            await autoRegister.browser.close();
            console.log('🔒 浏览器已关闭');
        }
    }
    
    console.log('');
    console.log('✅ 测试完成！');
}

// 运行测试
testBrowserProxy();
