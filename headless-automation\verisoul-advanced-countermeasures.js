/**
 * Verisoul 高级对抗措施
 * 基于逆向工程分析结果的精确对抗系统
 */

class VerisoulAdvancedCountermeasures {
    constructor() {
        this.isActive = false;
        this.detectionResults = new Map();
        this.countermeasures = new Map();
        this.originalFunctions = new Map();
    }

    /**
     * 启动高级对抗措施
     */
    async activate() {
        console.log('🛡️ 启动 Verisoul 高级对抗措施...');
        
        this.isActive = true;
        
        // 1. 预防性对抗 - 在 Verisoul 加载前执行
        this.applyPreventiveCountermeasures();
        
        // 2. 拦截性对抗 - 拦截和修改检测函数
        this.applyInterceptiveCountermeasures();
        
        // 3. 欺骗性对抗 - 提供虚假的检测结果
        this.applyDeceptiveCountermeasures();
        
        // 4. 行为性对抗 - 模拟真实用户行为
        this.applyBehavioralCountermeasures();
        
        console.log('✅ Verisoul 高级对抗措施已激活');
    }

    /**
     * 预防性对抗措施
     */
    applyPreventiveCountermeasures() {
        console.log('🚫 应用预防性对抗措施...');
        
        // 1. 阻止 Verisoul 脚本加载
        this.blockVerisoulScript();
        
        // 2. 预设环境变量
        this.presetEnvironmentVariables();
        
        // 3. 隐藏自动化特征
        this.hideAutomationFeatures();
    }

    /**
     * 阻止 Verisoul 脚本加载（可选）
     */
    blockVerisoulScript() {
        // 注意：这会完全阻止 Verisoul，可能导致验证失败
        // 仅在测试环境中使用
        
        const originalCreateElement = document.createElement;
        
        document.createElement = function(tagName) {
            const element = originalCreateElement.call(this, tagName);
            
            if (tagName.toLowerCase() === 'script') {
                const originalSetAttribute = element.setAttribute;
                
                element.setAttribute = function(name, value) {
                    if (name === 'src' && value.includes('verisoul')) {
                        console.log('🚫 阻止 Verisoul 脚本加载:', value);
                        return; // 不设置 src，阻止加载
                    }
                    return originalSetAttribute.call(this, name, value);
                };
            }
            
            return element;
        };
    }

    /**
     * 预设环境变量
     */
    presetEnvironmentVariables() {
        // 设置一致的虚拟指纹
        const fingerprint = this.generateConsistentFingerprint();
        
        // 重写 navigator 属性
        Object.defineProperties(navigator, {
            webdriver: {
                get: () => undefined,
                configurable: true
            },
            hardwareConcurrency: {
                get: () => fingerprint.hardwareConcurrency,
                configurable: true
            },
            deviceMemory: {
                get: () => fingerprint.deviceMemory,
                configurable: true
            },
            maxTouchPoints: {
                get: () => fingerprint.maxTouchPoints,
                configurable: true
            },
            platform: {
                get: () => fingerprint.platform,
                configurable: true
            },
            language: {
                get: () => fingerprint.language,
                configurable: true
            },
            languages: {
                get: () => fingerprint.languages,
                configurable: true
            }
        });
        
        // 重写 screen 属性
        Object.defineProperties(screen, {
            width: {
                get: () => fingerprint.screenWidth,
                configurable: true
            },
            height: {
                get: () => fingerprint.screenHeight,
                configurable: true
            },
            colorDepth: {
                get: () => fingerprint.colorDepth,
                configurable: true
            },
            pixelDepth: {
                get: () => fingerprint.pixelDepth,
                configurable: true
            }
        });
    }

    /**
     * 生成一致的指纹
     */
    generateConsistentFingerprint() {
        const seed = Date.now().toString();
        const hash = this.simpleHash(seed);
        
        return {
            hardwareConcurrency: 4 + (hash % 8),
            deviceMemory: [4, 8, 16][hash % 3],
            maxTouchPoints: hash % 2,
            platform: ['Win32', 'MacIntel', 'Linux x86_64'][hash % 3],
            language: ['en-US', 'en-GB', 'en-CA'][hash % 3],
            languages: [['en-US', 'en'], ['en-GB', 'en'], ['en-CA', 'en']][hash % 3],
            screenWidth: 1920 + (hash % 200 - 100),
            screenHeight: 1080 + (hash % 200 - 100),
            colorDepth: [24, 32][hash % 2],
            pixelDepth: [24, 32][hash % 2]
        };
    }

    /**
     * 简单哈希函数
     */
    simpleHash(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return Math.abs(hash);
    }

    /**
     * 隐藏自动化特征
     */
    hideAutomationFeatures() {
        // 删除 webdriver 相关属性
        delete navigator.__proto__.webdriver;
        delete window.navigator.webdriver;
        
        // 隐藏 Chrome 自动化标识
        if (window.chrome) {
            delete window.chrome.runtime.onConnect;
            delete window.chrome.runtime.onMessage;
        }
        
        // 隐藏 Selenium 特征
        const seleniumVars = [
            '__selenium_evaluate', '__selenium_unwrapped', '__webdriver_script_function',
            '__webdriver_script_func', '__webdriver_script_fn', '__fxdriver_evaluate',
            '__driver_unwrapped', '__webdriver_unwrapped', '__driver_evaluate',
            '__selenium_unwrapped', '__fxdriver_unwrapped'
        ];
        
        seleniumVars.forEach(varName => {
            if (window[varName]) {
                delete window[varName];
            }
        });
    }

    /**
     * 拦截性对抗措施
     */
    applyInterceptiveCountermeasures() {
        console.log('🎣 应用拦截性对抗措施...');
        
        // 1. 拦截 Verisoul 对象创建
        this.interceptVerisoulCreation();
        
        // 2. 拦截检测函数调用
        this.interceptDetectionFunctions();
        
        // 3. 拦截网络通信
        this.interceptNetworkCommunication();
    }

    /**
     * 拦截 Verisoul 对象创建
     */
    interceptVerisoulCreation() {
        const self = this;
        
        Object.defineProperty(window, 'Verisoul', {
            get() {
                return this._verisoul;
            },
            set(value) {
                console.log('🎯 拦截 Verisoul 对象创建');
                
                if (value && typeof value === 'object') {
                    // 修改 Verisoul 对象的方法
                    self.modifyVerisoulObject(value);
                }
                
                this._verisoul = value;
            },
            configurable: true
        });
    }

    /**
     * 修改 Verisoul 对象
     */
    modifyVerisoulObject(verisoulObj) {
        console.log('🔧 修改 Verisoul 对象方法...');
        
        // 修改 session 方法
        if (verisoulObj.session) {
            const originalSession = verisoulObj.session;
            
            verisoulObj.session = async function(...args) {
                console.log('🔐 拦截 session() 调用');
                
                try {
                    const result = await originalSession.apply(this, args);
                    
                    // 修改返回结果（如果需要）
                    if (result && result.session_id) {
                        console.log('✅ Session 调用成功:', result.session_id);
                    }
                    
                    return result;
                } catch (error) {
                    console.log('❌ Session 调用失败，返回模拟结果');
                    
                    // 返回模拟的 session 结果
                    return {
                        session_id: 'sim_' + Math.random().toString(36).substr(2, 9)
                    };
                }
            };
        }
    }

    /**
     * 拦截检测函数调用
     */
    interceptDetectionFunctions() {
        // 拦截常见的检测方法
        const detectionMethods = [
            'getOwnPropertyDescriptor',
            'getOwnPropertyNames',
            'getPrototypeOf',
            'hasOwnProperty'
        ];
        
        detectionMethods.forEach(method => {
            if (Object[method]) {
                const originalMethod = Object[method];
                
                Object[method] = function(...args) {
                    const result = originalMethod.apply(this, args);
                    
                    // 过滤敏感属性
                    if (method === 'getOwnPropertyNames' && args[0] === navigator) {
                        return result.filter(prop => prop !== 'webdriver');
                    }
                    
                    return result;
                };
            }
        });
    }

    /**
     * 拦截网络通信
     */
    interceptNetworkCommunication() {
        const originalFetch = window.fetch;
        const originalXHR = window.XMLHttpRequest;
        
        // 拦截 fetch 请求
        window.fetch = function(url, options) {
            if (url.includes('verisoul.ai')) {
                console.log('🌐 拦截 Verisoul 网络请求:', url);
                
                // 可以修改请求数据或返回模拟响应
                if (options && options.body) {
                    try {
                        const data = JSON.parse(options.body);
                        console.log('📤 请求数据:', data);
                        
                        // 这里可以修改发送的数据
                        // data.modified = true;
                        // options.body = JSON.stringify(data);
                    } catch (e) {
                        // 非 JSON 数据
                    }
                }
            }
            
            return originalFetch.apply(this, arguments);
        };
    }

    /**
     * 欺骗性对抗措施
     */
    applyDeceptiveCountermeasures() {
        console.log('🎭 应用欺骗性对抗措施...');
        
        // 1. Canvas 指纹欺骗
        this.spoofCanvasFingerprint();
        
        // 2. WebGL 指纹欺骗
        this.spoofWebGLFingerprint();
        
        // 3. 音频指纹欺骗
        this.spoofAudioFingerprint();
    }

    /**
     * Canvas 指纹欺骗
     */
    spoofCanvasFingerprint() {
        const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;
        
        CanvasRenderingContext2D.prototype.getImageData = function(sx, sy, sw, sh) {
            const imageData = originalGetImageData.call(this, sx, sy, sw, sh);
            
            // 添加微小的随机噪声
            for (let i = 0; i < imageData.data.length; i += 4) {
                if (Math.random() < 0.001) {
                    imageData.data[i] = Math.min(255, imageData.data[i] + Math.floor(Math.random() * 3) - 1);
                    imageData.data[i + 1] = Math.min(255, imageData.data[i + 1] + Math.floor(Math.random() * 3) - 1);
                    imageData.data[i + 2] = Math.min(255, imageData.data[i + 2] + Math.floor(Math.random() * 3) - 1);
                }
            }
            
            return imageData;
        };
    }

    /**
     * WebGL 指纹欺骗
     */
    spoofWebGLFingerprint() {
        const originalGetParameter = WebGLRenderingContext.prototype.getParameter;
        
        WebGLRenderingContext.prototype.getParameter = function(parameter) {
            const result = originalGetParameter.call(this, parameter);
            
            // 对特定参数返回随机化的结果
            if (parameter === this.RENDERER) {
                const renderers = [
                    'ANGLE (Intel, Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)',
                    'ANGLE (NVIDIA, NVIDIA GeForce GTX 1060 6GB Direct3D11 vs_5_0 ps_5_0, D3D11)',
                    'ANGLE (AMD, AMD Radeon RX 580 Series Direct3D11 vs_5_0 ps_5_0, D3D11)'
                ];
                return renderers[Math.floor(Math.random() * renderers.length)];
            }
            
            return result;
        };
    }

    /**
     * 音频指纹欺骗
     */
    spoofAudioFingerprint() {
        if (window.AudioContext || window.webkitAudioContext) {
            const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
            const originalCreateOscillator = OriginalAudioContext.prototype.createOscillator;
            
            OriginalAudioContext.prototype.createOscillator = function() {
                const oscillator = originalCreateOscillator.call(this);
                const originalStart = oscillator.start;
                
                oscillator.start = function(when) {
                    const randomDelay = Math.random() * 0.001;
                    return originalStart.call(this, when ? when + randomDelay : randomDelay);
                };
                
                return oscillator;
            };
        }
    }

    /**
     * 行为性对抗措施
     */
    applyBehavioralCountermeasures() {
        console.log('🎭 应用行为性对抗措施...');
        
        // 1. 模拟真实的鼠标行为
        this.simulateMouseBehavior();
        
        // 2. 模拟真实的键盘行为
        this.simulateKeyboardBehavior();
        
        // 3. 模拟真实的滚动行为
        this.simulateScrollBehavior();
    }

    /**
     * 模拟鼠标行为
     */
    simulateMouseBehavior() {
        setInterval(() => {
            if (!this.isActive) return;
            
            const x = Math.random() * window.innerWidth;
            const y = Math.random() * window.innerHeight;
            
            const event = new MouseEvent('mousemove', {
                clientX: x,
                clientY: y,
                bubbles: true,
                cancelable: true
            });
            
            document.dispatchEvent(event);
        }, 2000 + Math.random() * 3000);
    }

    /**
     * 模拟键盘行为
     */
    simulateKeyboardBehavior() {
        setInterval(() => {
            if (!this.isActive) return;
            
            // 模拟随机的按键事件（非可见字符）
            const keys = ['Shift', 'Control', 'Alt'];
            const randomKey = keys[Math.floor(Math.random() * keys.length)];
            
            const keydownEvent = new KeyboardEvent('keydown', {
                key: randomKey,
                bubbles: true,
                cancelable: true
            });
            
            const keyupEvent = new KeyboardEvent('keyup', {
                key: randomKey,
                bubbles: true,
                cancelable: true
            });
            
            document.dispatchEvent(keydownEvent);
            setTimeout(() => document.dispatchEvent(keyupEvent), 50 + Math.random() * 100);
        }, 10000 + Math.random() * 20000);
    }

    /**
     * 模拟滚动行为
     */
    simulateScrollBehavior() {
        setInterval(() => {
            if (!this.isActive) return;
            
            const scrollAmount = (Math.random() - 0.5) * 100;
            window.scrollBy(0, scrollAmount);
        }, 5000 + Math.random() * 10000);
    }

    /**
     * 停用对抗措施
     */
    deactivate() {
        console.log('🛑 停用 Verisoul 高级对抗措施');
        this.isActive = false;
    }

    /**
     * 获取对抗状态
     */
    getStatus() {
        return {
            isActive: this.isActive,
            countermeasuresApplied: this.countermeasures.size,
            detectionsBlocked: this.detectionResults.size
        };
    }
}

// 自动创建全局实例
if (typeof window !== 'undefined') {
    window.VerisoulAdvancedCountermeasures = VerisoulAdvancedCountermeasures;
    window.verisoulCountermeasures = new VerisoulAdvancedCountermeasures();
    
    console.log('🛡️ Verisoul 高级对抗系统已加载');
    console.log('使用 window.verisoulCountermeasures.activate() 激活对抗措施');
}

module.exports = VerisoulAdvancedCountermeasures;
