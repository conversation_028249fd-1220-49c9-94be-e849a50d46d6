# Real Browser Automation 使用指南

## 快速开始

### 1. 基础测试

首先运行基础测试确保环境正常：

```bash
npm run test-real-browser
```

这将：
- 启动真实浏览器
- 导航到 Google 页面
- 测试截图和HTML保存功能
- 测试基本页面交互

### 2. 配置环境变量

复制配置文件并根据需要修改：

```bash
cp real-browser-automation/.env.example .env
```

### 3. 运行邮箱验证

```bash
npm run real-browser-verification
```

## 配置选项详解

### 基础配置

```env
# 是否启用代理
REAL_BROWSER_PROXY=false

# 是否启用验证码自动解决
REAL_BROWSER_RECAPTCHA_SOLVE=false
```

### 代理配置

```env
REAL_BROWSER_PROXY=true
PROXY_URL=us2.cliproxy.io:3010
PROXY_USER=lovh89107-region-SG
PROXY_PASS=your_password
```

### 验证码配置

```env
REAL_BROWSER_RECAPTCHA_SOLVE=true
YESCAPTCHA_CLIENT_KEY=your_yescaptcha_key
```

## 使用场景

### 场景1: 完全手动模式

适合调试和观察流程：

```env
REAL_BROWSER_PROXY=false
REAL_BROWSER_RECAPTCHA_SOLVE=false
```

- 不使用代理
- 验证码需要手动处理
- 可以观察整个自动化过程

### 场景2: 使用代理但手动验证码

适合需要特定IP但想手动控制验证码的情况：

```env
REAL_BROWSER_PROXY=true
REAL_BROWSER_RECAPTCHA_SOLVE=false
PROXY_URL=your_proxy_host:port
PROXY_USER=your_username
PROXY_PASS=your_password
```

### 场景3: 完全自动化

适合批量处理：

```env
REAL_BROWSER_PROXY=true
REAL_BROWSER_RECAPTCHA_SOLVE=true
PROXY_URL=your_proxy_host:port
PROXY_USER=your_username
PROXY_PASS=your_password
YESCAPTCHA_CLIENT_KEY=your_key
```

## 调试功能

### 自动记录

每一步都会自动保存：

- **截图**: `real-browser-automation/screenshots/STEP_XX_stepname.png`
- **HTML**: `real-browser-automation/html/STEP_XX_stepname.html`
- **日志**: `real-browser-automation/logs/real-browser-YYYY-MM-DD.log`

### 错误记录

发生错误时会保存：

- **错误截图**: `screenshots/ERROR_XX_errorname.png`
- **错误HTML**: `html/ERROR_XX_errorname.html`

### 查看日志

```bash
# 查看最新日志
tail -f real-browser-automation/logs/real-browser-$(date +%Y-%m-%d).log

# 查看所有日志文件
ls -la real-browser-automation/logs/
```

## 常见问题

### Q: 浏览器启动失败

**A**: 检查是否安装了 Chrome/Chromium：

```bash
# Ubuntu/Debian
sudo apt-get install chromium-browser

# 或者安装 Google Chrome
wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
sudo sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list'
sudo apt-get update
sudo apt-get install google-chrome-stable
```

### Q: 代理连接失败

**A**: 检查代理配置：

1. 确认代理服务器地址和端口正确
2. 确认用户名和密码正确
3. 测试代理连接：

```bash
curl --proxy ***********************************:port http://httpbin.org/ip
```

### Q: 验证码解决失败

**A**: 检查 YesCaptcha 配置：

1. 确认 API 密钥正确
2. 检查账户余额
3. 查看 YesCaptcha 服务状态

### Q: 邮箱验证码获取失败

**A**: 检查 OneMailAPI 配置：

1. 确认 API 密钥正确
2. 检查网络连接
3. 查看 OneMailAPI 服务状态

## 高级用法

### 自定义超时时间

```env
# 页面加载超时 (毫秒)
PAGE_TIMEOUT=30000

# 邮箱验证码检查超时 (毫秒)
EMAIL_CHECK_TIMEOUT=120000

# 邮箱验证码检查间隔 (毫秒)
EMAIL_CHECK_INTERVAL=5000
```

### 禁用某些功能

```env
# 禁用截图保存
SAVE_SCREENSHOTS=false

# 禁用HTML保存
SAVE_HTML=false

# 禁用调试模式
DEBUG_MODE=false
```

## 与 headless-automation 的区别

| 特性 | headless-automation | real-browser-automation |
|------|-------------------|------------------------|
| 浏览器类型 | Headless (不可见) | Real Browser (可见) |
| 反检测能力 | 中等 | 强 |
| 调试便利性 | 低 | 高 |
| 资源消耗 | 低 | 高 |
| 适用场景 | 批量自动化 | 调试和高要求场景 |

## 性能优化建议

1. **合理设置超时时间**: 根据网络情况调整超时设置
2. **选择性保存文件**: 生产环境可以禁用截图和HTML保存
3. **代理选择**: 使用稳定的住宅代理提高成功率
4. **资源监控**: 监控内存和CPU使用情况

## 故障排除步骤

1. **运行基础测试**: `npm run test-real-browser`
2. **检查配置文件**: 确认 `.env` 文件配置正确
3. **查看日志文件**: 检查详细错误信息
4. **检查截图**: 查看失败时的页面状态
5. **测试网络连接**: 确认代理和API服务可用
