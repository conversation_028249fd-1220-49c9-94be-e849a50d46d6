/**
 * Verisoul 反检测系统测试脚本
 * 用于验证反检测措施的有效性
 */

const puppeteer = require('puppeteer');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const puppeteerExtra = require('puppeteer-extra');
const VerisoulAntiDetection = require('./verisoul-anti-detection.js');
const AntiFingerprint = require('./anti-fingerprint-config.js');

puppeteerExtra.use(StealthPlugin());

class VerisoulDetectionTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.verisoulAntiDetection = new VerisoulAntiDetection();
    }

    async runTest() {
        console.log('🧪 开始 Verisoul 反检测系统测试...');
        
        try {
            await this.initBrowser();
            await this.testFingerprinting();
            await this.testBehaviorSimulation();
            await this.testVerisoulDetection();
            
            console.log('✅ 所有测试通过！');
            
        } catch (error) {
            console.error('❌ 测试失败:', error.message);
            throw error;
        } finally {
            await this.cleanup();
        }
    }

    async initBrowser() {
        console.log('🚀 初始化浏览器...');
        
        const launchOptions = AntiFingerprint.getEnhancedLaunchOptions();
        this.browser = await puppeteerExtra.launch(launchOptions);
        this.page = await this.browser.newPage();
        
        // 注入反检测脚本
        await this.verisoulAntiDetection.injectPreloadScript(this.page);
        
        console.log('✅ 浏览器初始化完成');
    }

    async testFingerprinting() {
        console.log('🔍 测试指纹伪装...');
        
        await this.page.goto('data:text/html,<html><body><h1>Fingerprint Test</h1></body></html>');
        
        const fingerprint = await this.page.evaluate(() => {
            return {
                webdriver: navigator.webdriver,
                hardwareConcurrency: navigator.hardwareConcurrency,
                deviceMemory: navigator.deviceMemory,
                maxTouchPoints: navigator.maxTouchPoints,
                platform: navigator.platform,
                language: navigator.language,
                languages: navigator.languages,
                screenWidth: screen.width,
                screenHeight: screen.height,
                colorDepth: screen.colorDepth,
                pixelDepth: screen.pixelDepth,
                timezoneOffset: new Date().getTimezoneOffset(),
                hasChrome: !!window.chrome,
                pluginsLength: navigator.plugins.length
            };
        });

        console.log('📊 当前指纹信息:');
        console.log(JSON.stringify(fingerprint, null, 2));

        // 验证关键指纹是否被正确伪装
        const checks = [
            { name: 'WebDriver 隐藏', pass: fingerprint.webdriver === undefined },
            { name: 'Chrome 对象存在', pass: fingerprint.hasChrome },
            { name: '插件列表非空', pass: fingerprint.pluginsLength > 0 },
            { name: '硬件信息合理', pass: fingerprint.hardwareConcurrency >= 4 && fingerprint.hardwareConcurrency <= 16 },
            { name: '内存信息合理', pass: [4, 8, 16].includes(fingerprint.deviceMemory) },
            { name: '屏幕分辨率合理', pass: fingerprint.screenWidth > 1000 && fingerprint.screenHeight > 600 }
        ];

        console.log('🔍 指纹检查结果:');
        checks.forEach(check => {
            const status = check.pass ? '✅' : '❌';
            console.log(`${status} ${check.name}`);
        });

        const allPassed = checks.every(check => check.pass);
        if (!allPassed) {
            throw new Error('指纹伪装测试失败');
        }

        console.log('✅ 指纹伪装测试通过');
    }

    async testBehaviorSimulation() {
        console.log('🎭 测试行为模拟...');
        
        await this.page.goto('data:text/html,<html><body><h1>Behavior Test</h1><input id="test-input" /><button id="test-button">Test</button></body></html>');
        
        // 启动行为模拟
        await this.verisoulAntiDetection.startBehaviorSimulation(this.page);
        
        // 等待一段时间观察行为模拟
        console.log('⏳ 观察行为模拟 10 秒...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // 检查是否有模拟行为
        const behaviorEvents = await this.page.evaluate(() => {
            let mouseEvents = 0;
            let scrollEvents = 0;
            
            document.addEventListener('mousemove', () => mouseEvents++);
            window.addEventListener('scroll', () => scrollEvents++);
            
            return new Promise(resolve => {
                setTimeout(() => {
                    resolve({ mouseEvents, scrollEvents });
                }, 3000);
            });
        });

        console.log('📊 行为事件统计:', behaviorEvents);
        
        // 停止行为模拟
        await this.verisoulAntiDetection.stopBehaviorSimulation(this.page);
        
        console.log('✅ 行为模拟测试通过');
    }

    async testVerisoulDetection() {
        console.log('🎯 测试 Verisoul 检测对抗...');
        
        // 模拟 Verisoul 脚本加载
        await this.page.evaluate(() => {
            // 模拟 Verisoul 对象
            window.Verisoul = {
                session: function() {
                    return Promise.resolve({
                        session_id: 'test-session-' + Math.random().toString(36).substr(2, 9)
                    });
                }
            };
            
            console.log('🔍 模拟 Verisoul 已加载');
        });

        // 测试 Verisoul 检测
        const isLoaded = await this.verisoulAntiDetection.isVerisoulLoaded(this.page);
        if (!isLoaded) {
            throw new Error('Verisoul 检测失败');
        }

        // 应用对抗措施
        await this.verisoulAntiDetection.applyAdvancedCountermeasures(this.page);
        
        // 测试 Verisoul session 调用
        const sessionResult = await this.page.evaluate(async () => {
            try {
                const session = await window.Verisoul.session();
                return { success: true, sessionId: session.session_id };
            } catch (error) {
                return { success: false, error: error.message };
            }
        });

        console.log('📊 Verisoul session 测试结果:', sessionResult);
        
        if (!sessionResult.success) {
            throw new Error('Verisoul session 调用失败');
        }

        console.log('✅ Verisoul 检测对抗测试通过');
    }

    async testCanvasFingerprinting() {
        console.log('🎨 测试 Canvas 指纹干扰...');
        
        const canvasFingerprints = [];
        
        // 生成多个 Canvas 指纹，检查是否有差异
        for (let i = 0; i < 3; i++) {
            const fingerprint = await this.page.evaluate(() => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 绘制测试图案
                ctx.textBaseline = 'top';
                ctx.font = '14px Arial';
                ctx.fillText('Canvas fingerprint test 🔍', 2, 2);
                
                // 获取图像数据
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                
                // 计算简单哈希
                let hash = 0;
                for (let i = 0; i < imageData.data.length; i += 4) {
                    hash += imageData.data[i] + imageData.data[i + 1] + imageData.data[i + 2];
                }
                
                return hash;
            });
            
            canvasFingerprints.push(fingerprint);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.log('📊 Canvas 指纹结果:', canvasFingerprints);
        
        // 检查指纹是否有差异（表明干扰生效）
        const uniqueFingerprints = [...new Set(canvasFingerprints)];
        if (uniqueFingerprints.length === 1) {
            console.log('⚠️ Canvas 指纹未发生变化，可能需要调整干扰强度');
        } else {
            console.log('✅ Canvas 指纹干扰生效');
        }
    }

    async testWebGLFingerprinting() {
        console.log('🎮 测试 WebGL 指纹干扰...');
        
        const webglInfo = await this.page.evaluate(() => {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                return { error: 'WebGL not supported' };
            }
            
            return {
                vendor: gl.getParameter(gl.VENDOR),
                renderer: gl.getParameter(gl.RENDERER),
                version: gl.getParameter(gl.VERSION),
                shadingLanguageVersion: gl.getParameter(gl.SHADING_LANGUAGE_VERSION)
            };
        });

        console.log('📊 WebGL 信息:', webglInfo);
        
        if (webglInfo.error) {
            console.log('⚠️ WebGL 不支持，跳过测试');
        } else {
            console.log('✅ WebGL 指纹信息已获取');
        }
    }

    async cleanup() {
        if (this.browser) {
            try {
                await this.browser.close();
            } catch (error) {
                console.error('清理浏览器时出错:', error.message);
            }
        }
    }
}

// 运行测试
async function runTest() {
    const test = new VerisoulDetectionTest();
    
    try {
        await test.runTest();
        console.log('🎉 Verisoul 反检测系统测试完成！');
    } catch (error) {
        console.error('💥 测试失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此文件
if (require.main === module) {
    runTest();
}

module.exports = VerisoulDetectionTest;
