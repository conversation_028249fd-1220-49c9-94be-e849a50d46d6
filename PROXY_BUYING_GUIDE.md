# 代理购买指南

## 为什么需要住宅代理？

您遇到的"前几次成功，后面失败"的问题，主要是因为：
1. **浏览器指纹识别** - 相同的设备指纹被识别为机器人
2. **IP信誉** - 数据中心IP容易被标记为高风险
3. **行为模式** - 重复的自动化行为被检测

住宅代理可以解决这些问题，因为它们使用真实家庭用户的IP地址。

## 推荐的代理服务商

### 1. Smartproxy (性价比最高) ⭐⭐⭐⭐⭐
- **价格**: $75/月 (5GB) 或 $225/月 (25GB)
- **优势**: 价格合理，质量稳定，支持会话保持
- **购买链接**: https://smartproxy.com/
- **适合**: 中小规模自动化项目

### 2. Oxylabs (质量最好) ⭐⭐⭐⭐⭐
- **价格**: $300/月 (20GB) 起
- **优势**: 业界领先，成功率高，技术支持好
- **购买链接**: https://oxylabs.io/
- **适合**: 商业级项目，对稳定性要求高

### 3. Bright Data (功能最全) ⭐⭐⭐⭐
- **价格**: $500/月 (40GB) 起
- **优势**: 功能最全面，覆盖国家最多
- **购买链接**: https://brightdata.com/
- **适合**: 大规模项目，需要全球覆盖

### 4. NetNut (速度最快) ⭐⭐⭐⭐
- **价格**: $350/月 (20GB) 起
- **优势**: 延迟低，速度快，专注住宅代理
- **购买链接**: https://netnut.io/
- **适合**: 对速度要求高的项目

## 购买步骤

### 步骤1: 选择服务商
建议从 **Smartproxy** 开始，性价比最高。

### 步骤2: 注册账户
1. 访问 https://smartproxy.com/
2. 点击 "Get Started" 注册账户
3. 验证邮箱

### 步骤3: 选择套餐
推荐选择：
- **Residential Proxies** (住宅代理)
- **5GB/月** 套餐 ($75/月)
- **按流量计费** (Pay-as-you-go)

### 步骤4: 配置代理
购买后，在控制面板获取：
- **端点地址**: gate.smartproxy.com:10000
- **用户名**: 您的用户名
- **密码**: 您的密码

### 步骤5: 配置环境变量
在 `.env` 文件中添加：
```
RESIDENTIAL_PROXY_USER=your_smartproxy_username
RESIDENTIAL_PROXY_PASS=your_smartproxy_password
RESIDENTIAL_PROXY_ENDPOINT=gate.smartproxy.com:10000
RESIDENTIAL_PROXY_COUNTRY=US
```

## 使用新的代理配置

### 修改代码使用住宅代理
```javascript
// 创建代理处理器，使用住宅代理
const proxyHandler = new ProxyHandler({
    type: 'residential', // 使用住宅代理
    residential: {
        endpoint: process.env.RESIDENTIAL_PROXY_ENDPOINT,
        username: process.env.RESIDENTIAL_PROXY_USER,
        password: process.env.RESIDENTIAL_PROXY_PASS,
        country: 'US'
    }
});

// 获取代理配置
const proxyConfig = await proxyHandler.getValidProxy();
```

### 在浏览器中使用
```javascript
const browser = await playwright.chromium.launch({
    headless: false,
    proxy: proxyHandler.getBrowserProxyConfig()
});
```

## 预期效果

使用住宅代理后，您应该看到：
1. **成功率提升** - 不再出现"前几次成功后面失败"的问题
2. **更真实的身份** - 每次使用不同的住宅IP
3. **更长的会话时间** - 可以保持10分钟的稳定会话
4. **更好的地理分布** - IP来自真实的家庭用户

## 成本估算

以 Smartproxy 为例：
- **5GB/月**: $75 (约500元人民币)
- **预计使用量**: 每次验证约10-20MB
- **可支持**: 250-500次验证/月

## 注意事项

1. **流量消耗**: 住宅代理按流量计费，注意控制使用量
2. **会话保持**: 利用会话保持功能，避免频繁切换IP
3. **地理位置**: 选择与目标服务匹配的国家/地区
4. **备用方案**: 保留花生代理作为备用选择

## 测试建议

购买后先小规模测试：
1. 配置住宅代理
2. 运行5-10次验证
3. 观察成功率是否稳定
4. 确认无问题后扩大使用

## 技术支持

如果遇到问题：
1. 查看代理服务商的文档
2. 联系客服获取技术支持
3. 检查代理配置是否正确
4. 确认账户余额充足
